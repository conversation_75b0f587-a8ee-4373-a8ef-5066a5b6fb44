<?php

namespace App\Services;

use App\Traits\EpTrait;

class SSHService
{
    private function getValueJpn($val)
    {
        if (array_key_exists('value', $val)) {
            return $val["value"];
        }
        return '';
    }

    public function getMyIdentityInfo($icNo)
    {
        $icObj = array();
        $dataResp = array();
        $icno = trim(str_replace("-", "", $icNo));
        $uuid = \Ramsey\Uuid\Uuid::uuid4();
        $rqUid = $uuid . ' eps';
        $url = "http://192.168.63.205:7011/GetMyIdentityInfo/v1.0?wsdl";

        $headers = array(
            "Content-type: text/xml; charset=utf-8",
            "SOAPAction: http://www.ep.gov.my/Service/1-0/GetMyIdentityInfo/inquire",
        );
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:get='http://www.ep.gov.my/Schema/1-0/GetMyIdentityInfo'>    <soapenv:Header/>    <soapenv:Body xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><EPMFRq xmlns='http://www.ep.gov.my/Schema/1-0/GetMyIdentityInfo'><ns1:RqHeader xmlns:ns1='http://www.ep.gov.my/Schema/1-0/epmf'><ns1:ConsumerID>EPP-001</ns1:ConsumerID><ns1:UID><ns1:RqUID>$rqUid</ns1:RqUID></ns1:UID></ns1:RqHeader><GetMyIdentityInfoRq><Type>Basic</Type><ICNumber>$icno</ICNumber></GetMyIdentityInfoRq></EPMFRq></soapenv:Body></soapenv:Envelope>";

        try {

            $soap_do = curl_init();

            curl_setopt($soap_do, CURLOPT_URL, $url);
            curl_setopt($soap_do, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($soap_do, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($soap_do, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($soap_do, CURLOPT_POST, true);
            curl_setopt($soap_do, CURLOPT_POSTFIELDS, $xmlContents);


            $output = curl_exec($soap_do);

            $p = xml_parser_create();
            xml_parse_into_struct($p, $output, $vals, $index);
            xml_parser_free($p);
            foreach ($vals as $val) {
                if ($val["tag"] == 'EPMF:STATUSCODE')
                    $icObj["messagecode"] = $val["value"];
                if ($val["tag"] == 'EPMF:SEVERITY')
                    $icObj["severity"] = $val["value"];
                if ($val["tag"] == 'EPMF:STATUSDESC')
                    $icObj["message"] = $val["value"];
                if ($val["tag"] == 'GET:ICNUMBER')
                    $icObj["icno"] = $val["value"];
                if ($val["tag"] == 'GET:NAME')
                    $icObj["name"] = $val["value"];
                if ($val["tag"] == 'GET:DATEOFBIRTH')
                    $icObj["date_of_birth"] = $val["value"];
                if ($val["tag"] == 'GET:GENDER')
                    $icObj["gender"] = $val["value"] . " - " . EpTrait::$JPN_GENDER[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RACE')
                    $icObj["race"] = $val["value"] . " - " . EpTrait::$JPN_RACE[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RELIGION')
                    $icObj["religion"] = $val["value"] . " - " . EpTrait::$JPN_RELIGION[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:PERMANENTADDR1')
                    $icObj["addr1"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDR2')
                    $icObj["addr2"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDR3')
                    $icObj["addr3"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDRPOSTCODE')
                    $icObj["postcode"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRCITYCODE')
                    $icObj["city_code"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRCITYDESC')
                    $icObj["city"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRSTATECODE')
                    $icObj["statecode"] = $val["value"] . " - " . EpTrait::$JPN_STATE[$val["value"]];
                if ($val["tag"] == 'GET:RESIDENTSTATUS')
                    $icObj["residential_status"] = $val["value"] . " - " . EpTrait::$JPN_RESIDENTIAL[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RECORDSTATUS')
                    $icObj["record_status"] = $val["value"] . " - " . EpTrait::$JPN_RECORD_STATUS[$this->getValueJpn($val)];
            }
            $dataResp['result'] = $icObj;
            $dataResp['status'] = 'success';
            $dataResp['statusDesc'] = 'Completed';
        } catch (Exception $ex) {
            $status = 'error';
            $statusDesc = $ex->getMessage();

            $dataResp['result'] = $icObj;
            $dataResp['status'] = $status;
            $dataResp['statusDesc'] = $statusDesc;
        }
        return $dataResp;
    }
}