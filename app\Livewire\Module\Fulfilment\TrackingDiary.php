<?php

namespace App\Livewire\Module\Fulfilment;

use App\Livewire\Forms\Fulfilment\TrackingDiarySearchForm;
use App\Services\FulfilmentService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;


class TrackingDiary extends Component
{
    protected FulfilmentService $fulfilmentService;

    public TrackingDiarySearchForm $trackingSearchForm;

    // Public properties to store data
    public $listdata = [];
    public $supplier = null;
    public $smAppl = null;
    public $preparedPtj = null;
    public $issuedPtj = null;
    public $createdPtj = null;
    public $chargePtj = null;
    public $poco_no = null;
    public $sqInfo = null;
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false;

    // Properties for modal
    public $isModalOpen = false;
    public $modalTitle = '';
    public $modalType = '';
    public $modalDocNo = '';
    public $listDataWorkflow = [];
    public $listDataDoFn = [];
    public $listDataYepMenu = [];
    public $modalData = [];
    public $isLoading = false;

    public function boot(FulfilmentService $fulfilmentService)
    {
        $this->fulfilmentService = $fulfilmentService;
    }

    public function mount()
    {
        if ($this->carian) {
            $this->trackingSearchForm->documentNumber = $this->carian;
            $this->search();
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-tracking-diary-table');
    }

    private function hasCodiRole(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('role_codi_ep');
    }

    private function hasGMRole(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('role_gm');
    }

    public function getListDocNoTracking($docNo)
    {
        $this->carian = $docNo;
        $this->errorMessage = null;

        //Search Default SQ to PO
        $list = $this->fulfilmentService->getListDocNoFulfillmentSQPOTracking($docNo);
        $searchType = 1;

        //Search carian contractNo only.
        if (count($list) == 0) {
            $typeContractNo = substr($docNo, 0, 1);
            $typeContractNo2 = substr($docNo, 0, 2);
            if ($typeContractNo2 == 'CT' || $typeContractNo == 'Z' || $typeContractNo == 'M') {
                $list = collect(array(['contract_doc_no' => $docNo]));
            }
        }

        //Search Default SQ to CO
        if (count($list) == 0) {
            $list = $this->fulfilmentService->getListDocNoFulfillmentSQCOTracking($docNo);
            $searchType = 2;
        }

        //Searching for QT to CO only
        if (count($list) == 0) {
            $list = $this->fulfilmentService->getListDocNoFulfillmentQTCOTracking($docNo);
            $searchType = 3;
        }

        //Searching for PRCR POCO only
        if (count($list) == 0) {
            $list = $this->fulfilmentService->getListDocNoFulfillmentPRCRPOCOTracking($docNo);
            $searchType = 4;
        }

        //Searching for LOA CT POCO only
        if (count($list) == 0) {
            $list = $this->fulfilmentService->getListDocNoFulfillmentLOACTCRCOTracking($docNo);
            $searchType = 5;
        }

        //Searching for AP only
        if (count($list) == 0) {
            $list = $this->fulfilmentService->getListDocNoMarketResearchTracking($docNo);
            $searchType = 6;
        }

        //Filter > Do not proceed in tracking diary if result more than 10. If will suffer the processing query data.
        if (count($list) > 20) {
            $this->errorMessage = 'This Document No. skip to process. Try search other document no related.';
            return;
        }

        $listDocNo = array();
        $listGroupIdTracking = array();
        if (count($list) > 0) {

            if (isset($list[0]->ap_doc_no) && strlen($list[0]->ap_doc_no) > 0) {
                array_push($listDocNo, $list[0]->ap_doc_no);
            }

            if (isset($list[0]->fo_doc_no) && strlen($list[0]->fo_doc_no) > 0) {
                array_push($listDocNo, $list[0]->fo_doc_no);
            }

            if (isset($list[0]->quote_no) && strlen($list[0]->quote_no) > 0) {
                array_push($listDocNo, $list[0]->quote_no);
            }

            if (isset($list[0]->request_note_no) && strlen($list[0]->request_note_no) > 0) {
                array_push($listDocNo, $list[0]->request_note_no);
            }

            if (isset($list[0]->loa_doc_no) && strlen($list[0]->loa_doc_no) > 0) {
                array_push($listDocNo, $list[0]->loa_doc_no);
            }

            if (isset($list[0]->ct_doc_no) && strlen($list[0]->ct_doc_no) > 0) {
                array_push($listDocNo, $list[0]->ct_doc_no);
            }

            if (isset($list[0]->qt_doc_no) && strlen($list[0]->qt_doc_no) > 0) {
                array_push($listDocNo, $list[0]->qt_doc_no);
            }

            foreach ($list as $obj) {
                if (isset($obj->fr_doc_no) && $obj->fr_doc_no && strlen($obj->fr_doc_no) > 0) {
                    array_push($listDocNo, $obj->fr_doc_no);
                    $objResult = $this->fulfilmentService->getGroupIdTrackingDiary($obj->fr_doc_no);
                    if ($objResult != null && $objResult->group_id != null) {
                        array_push($listGroupIdTracking, $objResult->group_id);
                    }
                }
            }
        }

        /**
         * If not found step 1 search, So just search in Tracking Diary
         */
        if (count($listDocNo) == 0 && count($listGroupIdTracking) == 0) {
            array_push($listDocNo, $docNo);
            $objResult = $this->fulfilmentService->getGroupIdTrackingDiary($docNo);
            if ($objResult != null && $objResult->group_id != null) {
                array_push($listGroupIdTracking, $objResult->group_id);
            }
        }

        $collect = collect([]);

        $collect->put('list_doc_no', array_unique($listDocNo));
        $collect->put('list_group_id', array_unique($listGroupIdTracking));

        $listResult = $this->fulfilmentService->getListTrackingDiary($collect);

        $this->supplier = null;
        $this->smAppl = null;
        $this->preparedPtj = null;
        $this->issuedPtj = null;
        $this->createdPtj = null;
        $this->chargePtj = null;
        $typeDoc = substr($docNo, 0, 2);
        $this->sqInfo = null;

        if ($typeDoc == 'SQ') {
            $this->sqInfo = DB::connection('oracle_nextgen_rpt')->table('sc_quote')->where('quote_no', $docNo)->first();
        }

        if ($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO') {
            $this->supplier = $this->fulfilmentService->getSupplierInfoByDocNo($docNo);
            $this->preparedPtj = $this->fulfilmentService->getPtjInfoByDocNo($docNo, 'PREPARED_PTJ');
            $this->issuedPtj = $this->fulfilmentService->getPtjInfoByDocNo($docNo, 'ISSUED_PTJ');
            $this->createdPtj = $this->fulfilmentService->getPtjInfoByDocNo($docNo, 'CREATED_PTJ');
            $this->chargePtj = $this->fulfilmentService->getPtjInfoByDocNo($docNo, 'CHARGE_PTJ');
        }

        if ($this->hasCodiRole()) {
            $smDocType = array('JR', 'JN', 'KR', 'KN', 'KU', 'KA', 'KB', 'KC');
            if (in_array($typeDoc, $smDocType) && !$this->hasGMAccess()) {
                $this->errorMessage = 'You are not authorized to search this document.';
                return;
            }
        }

        $this->poco_no = null;

        if (count($listResult) > 0) {
            foreach ($listResult as $rowData) {
                $typeDocTemp = substr($rowData->doc_no, 0, 2);
                if ($typeDocTemp == 'PO' || $typeDocTemp == 'CO') {
                    $this->poco_no = $rowData->doc_no;
                    break;
                }
            }

            $firstRow = $listResult->first();
            if ($firstRow->doc_type == 'SR') {
                //to get info SM_APPL
                $this->smAppl = DB::connection('oracle_nextgen_rpt')->table('SM_APPL ap')
                    ->join('PM_STATUS_DESC sd', 'ap.STATUS_ID', '=', 'sd.STATUS_ID')
                    ->where('sd.LANGUAGE_CODE', 'en')
                    ->where('appl_no', $firstRow->doc_no)
                    ->select('ap.*', 'sd.STATUS_NAME')
                    ->first();

                $this->supplier = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A')
                    ->join('SM_APPL as B', 'A.SUPPLIER_ID', '=', 'B.SUPPLIER_ID')
                    ->leftJoin('SM_MOF_ACCOUNT as D', 'D.SUPPLIER_ID', '=', 'A.SUPPLIER_ID')
                    ->where('B.APPL_NO', $firstRow->doc_no)
                    ->select('A.*')
                    ->addSelect('D.MOF_NO')
                    ->addSelect(DB::raw(" null  as sap_order_no"))
                    ->addSelect(DB::raw(" null  as fo_doc_no"))
                    ->first();
            }

            $this->listdata = $listResult;
        } else {
            $this->listdata = [];
        }
    }

    public function search()
    {
        $this->trackingSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->trackingSearchForm->documentNumber;
        $this->errorMessage = null;

        try {
            $this->getListDocNoTracking($this->carian);
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        } finally {
            $this->isSearching = false;
        }
    }

    public function searchTransactionDocNo($search)
    {
        $typeDoc = substr($search, 0, 2);
        $list = array();
        if ($typeDoc == 'PD') {
            $list = $this->fulfilmentService->getListPidByPdNo($search);
        }
        if ($typeDoc == 'PI') {
            $list = $this->fulfilmentService->getListPurchaseInquiryByPiNo($search);
        }
        if ($typeDoc == 'SQ') {
            $list = $this->fulfilmentService->getListSimpleQuoteBySqNo($search);
        }
        if ($typeDoc == 'RN') {
            $list = $this->fulfilmentService->getListRequestNoteByRnNo($search);
        }
        if ($typeDoc == 'PR' || $typeDoc == 'CR') {
            $list = $this->fulfilmentService->getListFulfilmenRequestByPrCr($search);
        }
        if ($typeDoc == 'PO' || $typeDoc == 'CO' || $typeDoc == 'FC' || $typeDoc == 'L0') {
            $list = $this->fulfilmentService->getListFulfilmenOrderByPoCoFc($search);
        }

        if ($typeDoc == 'JR' || $typeDoc == 'JN' || $typeDoc == 'KR' || $typeDoc == 'KN' || $typeDoc == 'KU' || $typeDoc == 'KA' || $typeDoc == 'KB' || $typeDoc == 'KC') {
            $list = $this->fulfilmentService->getListSMByApplNo($search);
        }
        if ($typeDoc == 'DO' || $typeDoc == '60') {
            $list = $this->fulfilmentService->getListDeliveryOrderByDoNo($search);
        }
        if ($typeDoc == 'FN') {
            $list = $this->fulfilmentService->getListFulfilmentNoteByFnNo($search);
        }
        if ($typeDoc == 'QT') {
            $list = $this->fulfilmentService->getListQuotationTenderByQtNo($search);
        }
        if ($typeDoc == 'LA') {
            $list = $this->fulfilmentService->getListLetterAcceptanceByLoaNo($search);
        }
        if ($typeDoc == 'SD') {
            $list = $this->fulfilmentService->getListStopInstructionBySdNo($search);
        }
        if ($typeDoc == 'AP') {
            $list = $this->fulfilmentService->getListApplicationNoByApNo($search);
        }
        if ($typeDoc == '60') {
            $listInv = $this->fulfilmentService->getListInvoiceByInvNo($search);
            foreach ($listInv as $data) {
                array_push($list, $data);
            }
        }
        if ($typeDoc == 'C0' || $typeDoc == 'B0' || $typeDoc == 'CN' || $typeDoc == 'DN') {
            $list = $this->fulfilmentService->getListAdjustmentByDocNo($search);
        }
        if ($typeDoc == 'PA') {
            $list = $this->fulfilmentService->getListPaymentAdviseByPaNo($search);
        }

        $supplier = null;
        if ($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO') {
            $supplier = $this->fulfilmentService->getSupplierInfoByDocNo($search);
        }

        return [
            'listdata' => $list,
            'supplier' => $supplier,
            'carian' => $search
        ];
    }

    public function openDocumentModal($docNo, $title, $type = 'workflow')
    {
        $this->modalDocNo = $docNo;
        $this->modalTitle = $title;
        $this->modalType = $type;
        $this->isLoading = true;
        $this->isModalOpen = true;

        try {
            switch ($type) {
                case 'workflow':
                    // Existing workflow data handling
                    $result = $this->searchTransactionDocNo($docNo);

                    $this->modalData = [
                        'docNo' => $docNo,
                        'details' => 'Document details for ' . $docNo,
                        'supplier' => $result['supplier'] ?? null,
                    ];

                    $this->listDataWorkflow = $result['listdata'] ?? [];
                    break;

                case 'dofn':
                    // Fetch DO/FN data
                    $this->listDataDoFn = $this->fulfilmentService->getListWorkFlowStatusDOFN($docNo) ?? [];
                    break;

                case 'yepmenu':
                    // Fetch YEP menu data
                    $this->listDataYepMenu = $this->fulfilmentService->getListYepMenuTaskList($docNo) ?? [];
                    break;

                default:
                    throw new \Exception("Unknown modal type: {$type}");
            }
        } catch (\Exception $e) {
            $this->modalData = [
                'docNo' => $docNo,
                'status' => 'Error',
                'details' => 'An error occurred while fetching data: ' . $e->getMessage()
            ];
            $this->listDataWorkflow = [];
            $this->listDataDoFn = [];
            $this->listDataYepMenu = [];
        } finally {
            $this->isLoading = false;
        }

        // Dispatch event to initialize the modal
        $this->dispatch('open-document-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->modalDocNo = '';
    }

    public function render()
    {
        return view('livewire.fulfilment.tracking-diary', [
            'listdata' => $this->listdata,
            'supplier' => $this->supplier,
            'smAppl' => $this->smAppl,
            'preparedPtj' => $this->preparedPtj,
            'issuedPtj' => $this->issuedPtj,
            'createdPtj' => $this->createdPtj,
            'chargePtj' => $this->chargePtj,
            'poco_no' => $this->poco_no,
            'sqInfo' => $this->sqInfo,
            'carian' => $this->carian,
            'errorMessage' => $this->errorMessage,
            'isSearching' => $this->isSearching
        ]);
    }
}