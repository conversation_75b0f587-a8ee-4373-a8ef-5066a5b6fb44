<?php

namespace App\Livewire\Module\Item;

use App\Livewire\Forms\Item\SupplierItemSearchForm;
use App\Services\ItemService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;


class SupplierItem extends Component
{
    protected ItemService $ItemService;

    public SupplierItemSearchForm $supplierItemSearchForm;

    // Public properties to store data
    public $listdata = []; 
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public function boot(ItemService $ItemService)
    {
        $this->itemService = $ItemService;
    }

    public function mount()
    {
        if($this->carian){
            $this->supplierItemSearchForm->carian = $this->carian;
            $this->search();
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-supplier-item-table');
    } 

    public function search()
    {
        $this->supplierItemSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->supplierItemSearchForm->carian;
        $this->errorMessage = null;

        try {
            $list = null;
            $carianTemp = trim(strtoupper($this->carian));
            $subStrCheck = substr($this->carian, 0, 3);
            
            $carianLen = strlen($carianTemp); 
            
            if($subStrCheck == '357' || $subStrCheck == '465'){
                $list = $this->itemService->getListProductSupplierPendingCodification('MOF_NO', $carianTemp);
            } else if($subStrCheck == 'eP-'){
                $list = $this->itemService->getListProductSupplierPendingCodification('EP_NO', $this->carian);
                $carianTemp = $this->carian;
            } else if($subStrCheck == 'RNC' || $subStrCheck == 'RNE'){
                $list = $this->itemService->getListProductSupplierPendingCodification('DOC_NO', $carianTemp);
            } else {
                $list = $this->itemService->getListProductSupplierPendingCodification('LIKE_SEARCH', '%'.$carianTemp.'%');
            }
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 

    public function render()
    {
        return view('livewire.item.supplier-item', [
            'listdata' => $this->listdata
        ]);
    }
}