<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // ENABLE TO LOG ALL QUERIES INFO
        // DB::listen(function ($query) {
        //     logger($query->sql, $query->bindings);
        //     logger("Query time: {$query->time}ms");

        //     // Capture a more comprehensive stack trace
        //     $trace = collect(debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 30))
        //         ->map(function ($frame, $index) {
        //             $file = $frame['file'] ?? 'unknown';
        //             $line = $frame['line'] ?? 'unknown';
        //             $class = $frame['class'] ?? '';
        //             $type = $frame['type'] ?? '';
        //             $function = $frame['function'] ?? '';

        //             return "[{$index}] {$file}:{$line} - {$class}{$type}{$function}";
        //         })
        //         ->all();

        //     logger('Query trace:', $trace);
        // });

        Blade::component('layouts.guest', 'layouts.guest');
    }
}