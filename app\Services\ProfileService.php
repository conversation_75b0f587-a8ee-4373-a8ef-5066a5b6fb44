<?php

namespace App\Services;

use DB;

class ProfileService
{

    public function getListUomByUomName($input)
    {
        $inputSearch = '%' . strtolower($input) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT * FROM pm_uom
                    WHERE record_status = 1 
                    AND ( LOWER(uom_name) LIKE ? OR LOWER(uom_code) LIKE ? ) 
                    ORDER BY uom_id asc",
            array($inputSearch, $inputSearch)
        );
        return $results;
    }

    public function getListAllUom()
    {
        $query = "
            SELECT * from pm_uom where record_status = 1 order by uom_id";
        $result = DB::connection('oracle_nextgen_rpt')->select($query, array());
        return $result;
    }

    public function getUserRoles($userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as pu');
        $query->join('PM_USER_ORG as puo', 'pu.USER_ID', '=', 'puo.USER_ID');
        $query->join('PM_USER_ROLE as pur', 'puo.USER_ORG_ID', '=', 'pur.USER_ORG_ID');
        $query->where('pu.record_status', 1);
        $query->where('puo.record_status', 1);
        $query->where('pur.record_status', 1);
        $query->where('pu.user_id', $userId);
        return $query->get();
    }


    public function getUser($loginId)
    {
        $query = "
            SELECT u.* ,
            (select max(login_date) as last_login_date from pm_login_history where user_id = u.user_id  and rownum < 2) as last_login_date
            FROM pm_user u 
            where u.login_id = ? ";
        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($loginId));
        return $result;
    }


    public function getLastSigningSPKIorGPKI($userId, $type)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('pm_digi_sign');
        //$query->where('original_data','like', '%'.$icno.'%');
        $query->where('created_by', $userId);
        if ($type != null && $type == 'SPKI') {
            $query->whereNotNull('spki_signed_data');
        }
        if ($type != null && $type == 'GPKI') {
            $query->whereNotNull('gpki_signed_data');
        }
        $query->orderBy('changed_date', 'desc');
        return $query->first();
    }

    public function getListUserRoles($loginId)
    {
        $query = " SELECT  r.role_desc, ur.role_code 
            FROM pm_user u ,pm_user_org uo ,pm_user_role ur,pm_role_desc r  
            WHERE u.user_id = uo.user_id  AND uo.user_org_id = ur.user_org_id  AND ur.role_code = r.role_code  
            AND uo.record_status = 1  AND ur.record_status = 1  
            AND r.language_code = 'ms' 
            AND u.login_id = ? 
            GROUP BY ur.role_code,r.role_desc ";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($loginId));
        return $result;
    }

    public function getUsersByProfileIdRole($orgProfileId, $roleCode)
    {
        $query = "select pop.org_profile_id, pop.org_type_id, pov.org_validity_id, pov.org_code, pov.org_name, puo.user_org_id,
                    pov.ag_office_id, pu.user_id, pu.user_name, pu.login_id, pu.identification_no, pr.role_code
                    from pm_org_profile pop, pm_org_validity pov, pm_user_org puo, pm_user pu, pm_user_role pur, pm_role pr
                    where pop.org_profile_id = pov.org_profile_id 
                    and puo.org_profile_id = pov.org_profile_id
                    and pu.user_id = puo.user_id
                    and pur.user_org_id = puo.user_org_id
                    and pr.role_code = pur.role_code
                    and pop.org_profile_id = ?
                    and pov.record_status = 1
                    and puo.record_status = 1
                    and pu.record_status = 1
                    and pur.record_status = 1
                    and pr.record_status = 1
                    and pr.role_code = ?
                    and pu.preferred_language = 'en'";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($orgProfileId, $roleCode));
        return $result;
    }

    public function getListUsersNotSyncAsInactive()
    {
        $query = "SELECT distinct A.USER_ID, a.login_id, a.USER_NAME,a.ORG_TYPE_ID,a.identification_no, b.user_org_id  FROM PM_USER A
        JOIN PM_USER_ORG B ON A.USER_ID = B.USER_ID
        JOIN PM_USER_ROLE C ON B.USER_ORG_ID = C.USER_ORG_ID
        JOIN PM_ORG_PROFILE D ON B.ORG_PROFILE_ID = D.ORG_PROFILE_ID
        JOIN PM_PARAMETER X ON D.ORG_TYPE_ID = X.PARAMETER_ID
        WHERE A.RECORD_STATUS = 1 AND B.RECORD_STATUS = 1 AND C.RECORD_STATUS = 1
        AND X.PARAMETER_TYPE = 'OT' AND X.PARAMETER_CODE IN ('STB', 'GLC', 'STG', 'LAT', 'STS')
        AND C.ROLE_CODE IN ('MARKET_RESEARCH', 'COMPANY_PROFILE') 
        AND NOT EXISTS ( SELECT H.USER_ID FROM PM_LOGIN_HISTORY H WHERE H.USER_ID = A.USER_ID AND TO_TIMESTAMP(H.LOGIN_DATE) >= TO_TIMESTAMP(C.CHANGED_DATE) )
        AND ADD_MONTHS ( C.CHANGED_DATE,12) < TRUNC(SYSDATE)
        UNION ALL 
        SELECT DISTINCT A.USER_ID, a.login_id, a.USER_NAME,a.ORG_TYPE_ID,a.identification_no, b.user_org_id  FROM PM_USER A
        JOIN PM_USER_ORG B ON A.USER_ID = B.USER_ID
        JOIN PM_USER_ROLE C ON B.USER_ORG_ID = C.USER_ORG_ID
        JOIN PM_ORG_PROFILE D ON B.ORG_PROFILE_ID = D.ORG_PROFILE_ID
        JOIN PM_PARAMETER X ON D.ORG_TYPE_ID = X.PARAMETER_ID
        WHERE A.RECORD_STATUS = 1 AND B.RECORD_STATUS = 1 AND C.RECORD_STATUS = 1 AND X.PARAMETER_TYPE = 'OT'
        AND X.PARAMETER_CODE IN ('STB', 'GLC', 'STG', 'LAT', 'STS') AND C.ROLE_CODE IN ('MARKET_RESEARCH', 'COMPANY_PROFILE')
        AND EXISTS ( SELECT H.USER_ID FROM PM_LOGIN_HISTORY H WHERE H.USER_ID = A.USER_ID AND TO_TIMESTAMP(H.LOGIN_DATE) >= TO_TIMESTAMP(C.CHANGED_DATE) )
        AND ADD_MONTHS ( C.CHANGED_DATE,24)  < TRUNC(SYSDATE)
        ";

        $result = DB::connection('oracle_nextgen_rpt')->select($query);
        return $result;
    }

    protected function getDuplicateRoleErrorService()
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT u.USER_ID, u.LOGIN_ID, u.ORG_TYPE_ID, ur.ROLE_CODE, count(*)
            FROM pm_user u
            JOIN pm_user_org uo ON u.USER_ID = uo.USER_ID
            JOIN pm_user_role ur ON uo.USER_ORG_ID = ur.USER_ORG_ID
            WHERE u.RECORD_STATUS = 1
            AND uo.RECORD_STATUS = 1
            AND ur.RECORD_STATUS = 1
            GROUP BY u.USER_ID, u.LOGIN_ID, u.ORG_TYPE_ID, ur.ROLE_CODE
            HAVING count(*) > 1
            FETCH FIRST 5 ROWS ONLY"
        );

        return $result;
    }

    protected function getDuplicateRoleErrorDetailService($userId, $roleCode)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT u.USER_ID, u.LOGIN_ID, u.ORG_TYPE_ID, ur.*
             FROM pm_user u, pm_user_org uo, pm_user_role ur
             WHERE u.USER_ID = uo.USER_ID
             AND uo.USER_ORG_ID = ur.USER_ORG_ID
             AND u.RECORD_STATUS = 1
             AND uo.RECORD_STATUS = 1
             AND ur.RECORD_STATUS = 1
             AND u.USER_ID = ?
             AND ur.role_code = ?
             ORDER BY ur.CHANGED_DATE asc",
            [$userId, $roleCode]
        );

        return $result;
    }

    protected function getUsersWithRoleIssuesService()
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "WITH UserLoginHistory AS (
                SELECT user_id, MAX(login_date) AS last_login_date
                FROM pm_login_history
                GROUP BY user_id
            ),
            UserLiferayRoles AS (
                SELECT u1.screenName AS login_id, COUNT(*) AS total_role_liferay
                FROM User_@EPLFY u1
                INNER JOIN UserGroupRole@EPLFY ugr ON u1.userId = ugr.userId
                INNER JOIN Role_@EPLFY r ON ugr.roleId = r.roleId
                GROUP BY u1.screenName
            )
            SELECT
                u.USER_ID,
                u.LOGIN_ID,
                u.USER_NAME,
                u.EMAIL,
                u.identification_no,
                u.ORG_TYPE_ID,
                uo.ORG_PROFILE_ID,
                ulh.last_login_date,
                COUNT(ur.ROLE_CODE) AS total_role_ep,
                COALESCE(ulr.total_role_liferay, 0) AS total_role_liferay,
                (COUNT(ur.ROLE_CODE) - COALESCE(ulr.total_role_liferay, 0)) AS diff_role
            FROM
                pm_user u
            INNER JOIN
                pm_user_org uo ON u.USER_ID = uo.USER_ID
            INNER JOIN
                pm_user_role ur ON uo.USER_ORG_ID = ur.USER_ORG_ID
            LEFT JOIN
                UserLoginHistory ulh ON u.user_id = ulh.user_id
            LEFT JOIN
                UserLiferayRoles ulr ON u.LOGIN_ID = ulr.login_id
            WHERE
                u.RECORD_STATUS = 1
                AND uo.RECORD_STATUS = 1
                AND ur.RECORD_STATUS = 1
            GROUP BY
                u.USER_ID,
                u.LOGIN_ID,
                u.USER_NAME,
                u.EMAIL,
                u.identification_no,
                u.ORG_TYPE_ID,
                uo.ORG_PROFILE_ID,
                ulh.last_login_date,
                ulr.total_role_liferay
            HAVING
                (COUNT(ur.ROLE_CODE) - COALESCE(ulr.total_role_liferay, 0)) <> 0"
        );

        return $result;
    }

}
