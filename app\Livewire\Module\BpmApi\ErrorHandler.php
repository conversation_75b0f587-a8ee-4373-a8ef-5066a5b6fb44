<?php

namespace App\Livewire\Module\BpmApi;

use Auth;
use Livewire\Component;
use App\Livewire\Forms\Bpm\ErrorHandlerSearchForm;
use App\Services\BpmApiService;
use App\Traits\BpmApiTrait;
use Log;

class <PERSON>rrorHandler extends Component
{
    use BpmApiTrait;

    public ErrorHandlerSearchForm $errorHandlerForm;
    public $taskTitleList = [];
    public $statusList = [];
    public $results = [];
    public $errorMessage = null;
    public $task_title = null;
    public $status_id = null; 
    public $selectedTask = null;
    public $selectedStatus = null;

    protected BpmApiService $bpmApiService;

    // protected $listeners = ['dropdown-selected' => 'setStatus'];

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('bpm.bpm-api.error-handler', $action);
    }

    public function boot(BpmApiService $bpmApiService)
    {
        $this->bpmApiService = $bpmApiService;
    }

    public function mount()
    {
        $this->taskTitleList = BpmApiTrait::$ERROR_HANDLER_LIST;
        if($this->task_title) {
            $this->errorHandlerForm->task_title = $this->task_title;
        }
        if($this->status_id) {
            $this->errorHandlerForm->status_id = $this->status_id;
        }
        
        $this->search();
    }

    public function setStatus($task)
    {
        $this->selectedTask = $task;
        $this->statusList = BpmApiTrait::$ERROR_HANDLER_LIST[$task];
        $this->selectedStatus = ''; // reset status when task changes
        
    }

    public function search()
    {
        $this->errorHandlerForm->validate();

        try {
        $task_title = $this->errorHandlerForm->task_title;
        $status_id = $this->errorHandlerForm->status_id;
        $data = collect([
            "task_title" => 'QT',
            "status_id" => '96006']);
        $results = $this->bpmApiService->apiServices('error_handler', $data);
        $this->results = $results;
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
            $this->results = null;
        }
    }

    public function render()
    {
        return view('livewire.bpm-api.error-handler', [
            'taskTitleList' => $this->taskTitleList,
            'result' => $this->results,
            'statusList' => $this->statusList
        ]);
    }
}
