<?php

namespace App\Livewire\Forms\Crm;

use Livewire\Attributes\Validate;
use Livewire\Form;

class PomsTalkdeskUploadForm extends Form
{
    #[Validate('required|file|mimes:csv|max:204800')]
    public $uploadFile;

    public function messages()
    {
        return [
            'uploadFile.required' => 'Please select a file to upload',
            'uploadFile.file' => 'The uploaded file is not valid',
            'uploadFile.mimes' => 'The file must be a CSV file',
            'uploadFile.max' => 'The file size must not exceed 10MB',
        ];
    }
}