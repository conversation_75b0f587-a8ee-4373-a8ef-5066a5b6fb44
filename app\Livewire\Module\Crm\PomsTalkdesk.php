<?php

namespace App\Livewire\Module\Crm;

use App\Livewire\Forms\Crm\PomsTalkdeskTypeForm;
use App\Livewire\Forms\Crm\PomsTalkdeskUploadForm;
use App\Services\PomsService;
use Auth;
use DateTime;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\Attributes\Url;
use Carbon\Carbon;
use Livewire\WithFileUploads;

class PomsTalkdesk extends Component
{
    use WithFileUploads;

    protected PomsService $pomsService;

    public PomsTalkdeskTypeForm $typeForm;
    public PomsTalkdeskUploadForm $uploadForm;

    public $data = [];
    #[Url]
    public $selectedType;

    // Add property to track selected file info
    public $selectedFileName = '';
    public $selectedFileSize = '';

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('crm.poms-talkdesk', $action);
    }

    public function boot(PomsService $pomsService)
    {
        $this->pomsService = $pomsService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        // Pre-populate form if URL has type value
        if ($this->selectedType) {
            $this->typeForm->type = $this->selectedType;
        }

        $this->loadTalkdeskData();
    }

    public function loadTalkdeskData()
    {
        $this->typeForm->validate();
        $this->selectedType = $this->typeForm->type;

        $this->uploadForm->reset();
        $this->selectedFileName = '';
        $this->selectedFileSize = '';
        $this->uploadForm->uploadFile = null;

        if ($this->typeForm->type === 'monthly') {
            $this->data = $this->pomsService->getTalkdeskMonthlyData();
        } else {
            $this->data = $this->pomsService->getTalkdeskData();
        }

        // Dispatch event to refresh datatables
        $this->dispatch('refresh-talkdesk-table');
    }

    /*
    // Testing uploadData function - commented out
    public function uploadData()
    {
        if (!$this->userCan('create')) {
            abort(403, 'You do not have permission to perform this action.');
            return;
        }

        // Validate both forms
        $this->typeForm->validate();
        $this->uploadForm->validate();

        $user = auth()->user()->user_name ?? 'unknown';
        $status_upload = 'success';
        $error_message = '';

        try {
            $type = $this->typeForm->type; // Use type from the existing form
            $file = $this->uploadForm->uploadFile;

            if (!$file) {
                throw new Exception('No file selected for upload.');
            }

            // Store the uploaded file in the storage/poms/talkdesk directory
            $originalName = $file->getClientOriginalName();
            $path = $file->storeAs('poms/talkdesk', $originalName, 'local');

            Log::info("PomsTalkdesk Upload Test - File stored", [
                'original_name' => $originalName,
                'path' => $path,
                'type' => $type,
                'user' => $user
            ]);

            // Open the file for reading
            $handle = fopen(Storage::disk('local')->path($path), 'r');

            if (!$handle) {
                throw new Exception('Could not open the uploaded file.');
            }

            // Read the first row (header)
            $header = fgetcsv($handle);
            Log::info("PomsTalkdesk Upload Test - Header", ['header' => $header]);

            $processedRows = 0;
            $skippedRows = 0;

            // Read the remaining rows (data)
            while ($row = fgetcsv($handle)) {
                try {
                    // Simulate checking if date_call already exists in the database
                    $slaTalkdeskTable = $type === 'daily' ? 'sla_mitel' : 'sla_talkdesk_monthly';

                    // For testing, we'll log what would be checked instead of actually checking
                    Log::info("PomsTalkdesk Upload Test - Would check existence in table", [
                        'table' => $slaTalkdeskTable,
                        'date_call' => $row[1]
                    ]);

                    // Map the data to the table fields using index numbers
                    $insertData = [
                        'date_call' => $row[1],
                        'acd_call_offer' => $row[2],
                        'acd_call_handle' => $row[3],
                        'call_WI_lvl' => $row[4],
                        'call_abandon_short' => $row[7],
                        'call_abandon_long' => $row[6],
                        'abandon_percentage' => number_format((float) str_replace('%', '', $row[8]), 2),
                        'answer_percentage' => number_format((float) str_replace('%', '', $row[9]), 2),
                        'service_level' => number_format((float) str_replace('%', '', $row[10]), 2),
                        'mitel_insert_data_datetime' => Carbon::now(),
                        'filename' => $originalName,
                        'periodicity' => $type,
                        'created_by' => $user
                    ];

                    // Log the data instead of inserting into database
                    Log::info("PomsTalkdesk Upload Test - Would insert data", [
                        'table' => $slaTalkdeskTable,
                        'data' => $insertData
                    ]);

                    $processedRows++;
                } catch (Exception $e) {
                    $status_upload = 'partial';
                    $error_message .= "Error processing row: {$e->getMessage()}. ";
                    Log::error("PomsTalkdesk Upload Test - Row processing error", [
                        'error' => $e->getMessage(),
                        'row' => $row
                    ]);
                }
            }

            // Close the file
            fclose($handle);

            Log::info("PomsTalkdesk Upload Test - Processing complete", [
                'processed_rows' => $processedRows,
                'skipped_rows' => $skippedRows,
                'status' => $status_upload
            ]);

            // Set success/partial success message
            if ($status_upload === 'success') {
                session()->flash('success', "TEST MODE: File processed successfully! Would have processed {$processedRows} rows.");
            } else {
                session()->flash('warning', "TEST MODE: File processed with warnings. Would have processed {$processedRows} rows, skipped {$skippedRows} rows. {$error_message}");
            }

            // Reset the upload form
            $this->uploadForm->reset();
            // Clear selected file info
            $this->selectedFileName = '';
            $this->selectedFileSize = '';

        } catch (Exception $e) {
            Log::error("PomsTalkdesk Upload Test - Upload failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', "TEST MODE: Upload failed: {$e->getMessage()}");
        }
    }
    */

    // Production uploadData function - writes to database
    public function uploadData()
    {
        if (!$this->userCan('create')) {
            abort(403, 'You do not have permission to perform this action.');
            return;
        }

        // Validate both forms
        $this->typeForm->validate();
        $this->uploadForm->validate();

        $user = auth()->user()->user_name ?? 'unknown';
        $status_upload = 'success';
        $error_message = '';

        try {
            $type = $this->typeForm->type; // Use type from the existing form
            $file = $this->uploadForm->uploadFile;

            if (!$file) {
                throw new Exception('No file selected for upload.');
            }

            // Store the uploaded file in the storage/poms/talkdesk directory
            $originalName = $file->getClientOriginalName();
            $path = $file->storeAs('poms/talkdesk', $originalName, 'local');

            // Open the file for reading
            $handle = fopen(Storage::disk('local')->path($path), 'r');

            if (!$handle) {
                throw new Exception('Could not open the uploaded file.');
            }

            // Read the first row (header)
            $header = fgetcsv($handle);

            $processedRows = 0;
            $skippedRows = 0;

            // Read the remaining rows (data)
            while ($row = fgetcsv($handle)) {
                try {
                    // Check if date_call already exists in the database
                    $slaTalkdeskTable = $type === 'daily' ? 'sla_mitel' : 'sla_talkdesk_monthly';
                    $date_call_exists = DB::connection('mysql_poms')
                        ->table($slaTalkdeskTable)
                        ->where('date_call', $row[1])
                        ->exists();

                    if ($date_call_exists) {
                        // Add error message if date_call already exists
                        $status_upload = 'partial';
                        $error_message .= "Row with Call Date {$row[1]} already exists and was skipped. ";
                        $skippedRows++;

                        continue;
                    }

                    // Map the data to the table fields using index numbers
                    $insertData = [
                        'date_call' => $row[1],
                        'acd_call_offer' => $row[2],
                        'acd_call_handle' => $row[3],
                        'call_WI_lvl' => $row[4],
                        'call_abandon_short' => $row[7],
                        'call_abandon_long' => $row[6],
                        'abandon_percentage' => number_format((float) str_replace('%', '', $row[8]), 2),
                        'answer_percentage' => number_format((float) str_replace('%', '', $row[9]), 2),
                        'service_level' => number_format((float) str_replace('%', '', $row[10]), 2),
                        'mitel_insert_data_datetime' => Carbon::now(),
                        'filename' => $originalName,
                        'periodicity' => $type,
                        'created_by' => $user
                    ];

                    // Insert the data into the database
                    DB::connection('mysql_poms')->table($slaTalkdeskTable)->insert($insertData);

                    $processedRows++;
                } catch (Exception $e) {
                    $status_upload = 'partial';
                    $error_message .= "Error processing row: {$e->getMessage()}. ";
                    Log::error("PomsTalkdesk Upload - Row processing error", [
                        'error' => $e->getMessage(),
                        'row' => $row
                    ]);
                }
            }

            // Close the file
            fclose($handle);

            // Set success/partial success message
            if ($status_upload === 'success') {
                session()->flash('success', "File uploaded successfully! Processed {$processedRows} rows.");
            } else {
                session()->flash('warning', "File uploaded with warnings. Processed {$processedRows} rows, skipped {$skippedRows} rows. {$error_message}");
            }

            // Reset the upload form and reload data
            $this->uploadForm->reset();
            // Clear selected file info
            $this->selectedFileName = '';
            $this->selectedFileSize = '';
            $this->loadTalkdeskData();

        } catch (Exception $e) {
            Log::error("PomsTalkdesk Upload - Upload failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            session()->flash('error', "Upload failed: {$e->getMessage()}");
        }
    }

    public function updatedUploadFormUploadFile()
    {
        if ($this->uploadForm->uploadFile) {
            $this->selectedFileName = $this->uploadForm->uploadFile->getClientOriginalName();
            $this->selectedFileSize = $this->formatFileSize($this->uploadForm->uploadFile->getSize());
        } else {
            $this->selectedFileName = '';
            $this->selectedFileSize = '';
        }
    }

    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    public function render()
    {
        return view('livewire.crm.poms-talkdesk', [
            'data' => $this->data,
            'byType' => $this->typeForm->type
        ]);
    }
}