<?php

namespace App\Livewire\Module\Profile;

use App\Livewire\Forms\Profile\OrganizationSearchForm;
use App\Services\OrganizationService;
use Auth;
use Livewire\Attributes\Url;
use Livewire\Component;

class OrganizationSearch extends Component
{
    public OrganizationSearchForm $organizationSearchForm;
    #[Url]
    public $searchValue = null;
    protected OrganizationService $organizationService;

    // Properties to store search results
    public $orgInfo = null;
    public $listData = null;
    public $totalDeliveryAddress = 0;
    public $searchResult = null;
    public $searchType = null;

    // For modal
    public $modalTitle;
    public $modalType;
    public $listDataAddresses;
    public $listDataUserGroup;
    public $isLoading = false;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.profile.organization', $action);
    }

    public function boot(OrganizationService $organizationService)
    {
        $this->organizationService = $organizationService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($this->searchValue) {
            $this->organizationSearchForm->search = $this->searchValue;
            $this->searchOrganization();
        }
    }

    public function rendered()
    {
        $this->dispatch('organizationUpdated');
    }

    protected function getSearchType($search)
    {
        $checkIcno = strlen(trim($search));

        if ($checkIcno === 12 || strlen(intval($search)) === 9) {
            return "identification_no";
        }

        return "org_code";
    }

    public function searchOrganization()
    {
        $this->organizationSearchForm->validate();
        $this->searchValue = $this->organizationSearchForm->search;

        $this->searchType = $this->getSearchType($this->searchValue);

        if ($this->searchType === "identification_no") {
            $icNo = $this->searchValue;
            $type = 'identification_no';
            $list = $this->getDetailUserList($type, $icNo);

            if (count($list) > 0) {
                $orgProfileId = $list[0]->org_profile_id;
                $type = 'org_profile_id';
                $this->orgInfo = $this->organizationService->getPmOrganization($type, $orgProfileId);
                $this->setPmOrgHierarchy($this->orgInfo);

                $resultCount = $this->organizationService->getListDeliveryAddressByPtj("COUNT", $orgProfileId);
                $this->totalDeliveryAddress = $resultCount[0]->total;
                $this->listData = $list;
            } else {
                $this->orgInfo = null;
                $this->listData = null;
                $this->searchResult = 'notfound';
                $this->totalDeliveryAddress = 0;
            }
        } else {
            $orgCode = $this->searchValue;
            $org_code_type = 'org_code';
            $this->orgInfo = $this->organizationService->getPmOrganization($org_code_type, $orgCode);
            $this->setPmOrgHierarchy($this->orgInfo);

            if (count($this->orgInfo) > 0) {
                $orgProfileId = $this->orgInfo[0]->org_profile_id;
                $org_profile_type = 'org_profile_id';
                $this->listData = $this->getDetailUserList($org_profile_type, $orgProfileId);
                $resultCount = $this->organizationService->getListDeliveryAddressByPtj("COUNT", $orgProfileId);
                $this->totalDeliveryAddress = $resultCount[0]->total;
            } else {
                $this->orgInfo = null;
                $this->listData = null;
                $this->searchResult = 'notfound';
                $this->totalDeliveryAddress = 0;
            }
        }
    }

    public function getDetailUserList($type, $value)
    {
        $list = $this->organizationService->getUserList($type, $value);
        if (count($list) > 0) {
            foreach ($list as $data) {
                $listUserRole = $this->organizationService->getUserRole($data->user_id);
                $isAdmin = '';
                $totalDeliveryAddressOfficer = 0;
                $isDeliveryAddressOfficer = false;
                foreach ($listUserRole as $userRole) {
                    $roleDescBM = $this->organizationService->getRoleDesc($userRole->role_code, 'ms');
                    if ($roleDescBM) {
                        $userRole->role_name = $userRole->role_name . ' , ' . $roleDescBM->role_name;
                    }

                    if ($userRole->role_code == 'PTJ_ADMIN' || $userRole->role_code == 'MINISTRY_ADMIN' || $userRole->role_code == 'JABATAN_ADMIN') {
                        $isAdmin = $roleDescBM->role_code . ' , ' . $roleDescBM->role_name;
                    }

                    if ($userRole->role_code == 'RECEIVING_OFFICER') {
                        $isDeliveryAddressOfficer = true;
                        $result = $this->organizationService->getListReceivingAddressByUser("COUNT", $data->org_profile_id, $data->identification_no);
                        $totalDeliveryAddressOfficer = $result[0]->total;
                    }

                    if ($userRole->approval_limit != null && $userRole->approval_limit > 0) {
                        $userRole->approval_limit = '(RM' . $userRole->approval_limit . ')';
                    }
                }

                // gpki roles 
                $gpkiRole = collect(['CT_APPROVER', 'BPK_CC_APPROVER', 'RN_APPROVER', 'FL_APPROVER', 'ACKNOWLEDGE_OFFICER', 'PAY_OFFICER', 'BPK_CQ_APPROVER', 'BPK_PQ_APPROVER', 'COMMITTEE_APPROVER', 'PUBLICATION_APPROVER', 'SC_CHAIRPERSON', 'EC_CHAIRPERSON', 'OC_CHAIRPERSON', 'TEC_CHAIRPERSON', 'FEC_CHAIRPERSON', 'QCA_CHAIRPERSON', 'QCB_CHAIRPERSON', 'PPB_CHAIRPERSON', 'FPB_CHAIRPERSON', 'FPB_CHAIRPERSON']);

                $isGpkiValid = false;
                $isGpkiValid = collect($listUserRole)->contains(function ($value, $key) use ($gpkiRole) {
                    return $gpkiRole->contains($value->role_code);
                });
                $data->is_gpki_user = $isGpkiValid;

                $data->role_admin = $isAdmin;
                $data->isDeliveryAddressOfficer = $isDeliveryAddressOfficer;
                $data->totalDeliveryAddressOfficer = $totalDeliveryAddressOfficer;


                $data->listUserRole = $listUserRole;

                $listApprover = $this->organizationService->getPmUserGroupByUserId($data->user_id);
                $data->listApprover = $listApprover;
            }
        }
        return $list;
    }

    public function setPmOrgHierarchy(&$data)
    {
        if (count($data) > 0) {
            if ($data[0]->parent_org_profile_id) {
                $orgProfileId = $data[0]->parent_org_profile_id;
                $hierarchy = array();
                for ($x = 0; $x < $data[0]->org_type_id - 2; $x++) {
                    if ($x == 0) {
                        ${"id" . $x} = $this->organizationService->getPmOrgValidityByParentId($orgProfileId);
                    } else {
                        ${"id" . $x} = $this->organizationService->getPmOrgValidityByParentId(${"id" . ($x - 1)}[0]->parent_org_profile_id);
                    }
                    $hierarchy[] = array('org_name' => ${"id" . $x}[0]->org_name, 'org_code' => ${"id" . $x}[0]->org_code, 'org_type' => ${"id" . $x}[0]->code_desc);
                }
                $data[0]->hierarchy = array_reverse($hierarchy);
            } else {
                $data[0]->hierarchy = null;
            }
        }
        return $data;
    }

    public function openOrganizationModal($orgProfileId, $title, $type, $icno = null)
    {
        $this->modalTitle = $title;
        $this->modalType = $type;
        $this->isLoading = true;

        try {
            switch ($type) {
                case 'delivery-addresses':
                    $this->listDataAddresses = $this->organizationService->getListDeliveryAddressByPtj("SELECT", $orgProfileId) ?? [];
                    $this->listDataUserGroup = [];
                    break;

                case 'usergroup':
                    $this->listDataUserGroup = $this->organizationService->getPmUserGroupByOrgProfileId($orgProfileId) ?? [];
                    $this->listDataAddresses = [];
                    break;

                case 'receiving-addresses':
                    if (!$icno) {
                        throw new \Exception("IC number is required for user addresses");
                    }
                    $this->listDataAddresses = $this->organizationService->getListReceivingAddressByUser("SELECT", $orgProfileId, $icno) ?? [];
                    $this->listDataUserGroup = [];
                    break;

                default:
                    throw new \Exception("Unknown modal type: {$type}");
            }
        } catch (\Exception $e) {
            $this->listDataAddresses = [];
            $this->listDataUserGroup = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-organization-modal');
    }

    public function closeModal()
    {
        $this->modalTitle = null;
        $this->modalType = null;
        $this->listDataAddresses = [];
        $this->listDataUserGroup = [];
    }

    public function render()
    {
        return view('livewire.profile.organization-search', [
            'orginfo' => $this->orgInfo,
            'listdata' => $this->listData,
            'totalDeliveryAddress' => $this->totalDeliveryAddress,
            'result' => $this->searchResult,
            'carian' => $this->searchValue,
            'type' => $this->searchType
        ]);
    }
}