<?php

namespace App\Livewire\Forms\Bpm;

use Livewire\Form;
use Livewire\Attributes\Validate;

class InstanceQuerySearchForm extends Form {

    #[Validate('required', message: 'Please select composite version')]
    public $composite = '';
    #[Validate('required', message: 'Please select created from')]
    public $from = '';
    #[Validate('required', message: 'Please select created to')]
    public $to = '';
    public $selectedState = []; 
}