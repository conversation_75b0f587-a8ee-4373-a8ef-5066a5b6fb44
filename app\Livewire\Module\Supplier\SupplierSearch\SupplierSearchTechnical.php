<?php
namespace App\Livewire\Module\Supplier\SupplierSearch;

use App\Livewire\Module\Supplier\SupplierSearch;
use App\Traits\EpTrait;

use Auth;
use Carbon\Carbon;

class SupplierSearchTechnical extends SupplierSearch
{
    use EpTrait;

    // Modal properties
    public $selectedId;
    public $selectedData;
    public $modalData;
    public $modalTitle;
    public $isModalOpen = false;
    public $isLoading = false;
    public $modalType = '';
    public $modalSubtitle = '';

    public function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.supplier-technical', $action);
    }

    public function searchSuppliers()
    {
        $search = trim($this->supplierSearchForm->search);
        if (empty($search)) {
            $this->results = [];
            return;
        }

        $searchType = $this->getSearchType($search);

        switch ($searchType) {
            case 'MOF':
                $list = $this->supplierService->getSMSupplierUsersDetailsByMofNOorEpNo($search, null);
                $this->processSupplierResults($list, $search, 'mofno');
                break;

            case 'ePNo':
                $list = $this->supplierService->getSMSupplierUsersDetailsByMofNOorEpNo(null, $search);
                $this->processSupplierResults($list, $search, 'epno');
                break;

            case 'IcNo':
                // Implement IC number search if needed
                break;

            case 'ApplNo':
                $epNo = $this->supplierService->getEpNoSmSupplierByApplNo($search);
                if ($epNo) {
                    $list = $this->supplierService->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
                    $this->processSupplierResults($list, $search, 'applno');
                } else {
                    $this->results = [
                        'listdata' => null,
                        'listXml' => null,
                        'type' => 'applno',
                        'result' => 'notfound',
                        'carian' => $search
                    ];
                }
                break;

            case 'SAP':
                $epNo = $this->supplierService->getEpNoBySapVendorCode($search);
                if ($epNo && strlen($epNo) > 0) {
                    $list = $this->supplierService->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
                    $this->processSupplierResults($list, $search, 'sap');
                } else {
                    $this->results = [
                        'listdata' => null,
                        'listXml' => null,
                        'type' => 'sap',
                        'result' => 'notfound',
                        'carian' => $search
                    ];
                }
                break;

            // WILL CHECK FOR BOTH NAME AND SSM NO and RETURN EP NO
            case 'SUPPLIER_NAME':
                $resultEpNo = $this->supplierService->getEpNoSmSupplier($search);
                if ($resultEpNo) {
                    $list = $this->supplierService->getSMSupplierUsersDetailsByMofNOorEpNo(null, $resultEpNo);
                    $this->processSupplierResults($list, $search, 'supplier_name');
                } else {
                    $this->results = [
                        'listdata' => null,
                        'listXml' => null,
                        'type' => 'supplier_name',
                        'result' => 'notfound',
                        'carian' => $search
                    ];
                }
                break;
        }

        // Update the URL parameter with the current search value
        $this->searchValue = $search;
    }

    protected function processSupplierResults($list, $searchValue, $searchType)
    {
        if (count($list) > 0) {
            $isNotAppointedAdmin = true;
            $isData = (object) [];

            /** Repopulate Data to bind others data * */
            foreach ($list as $data) {
                //populate put to $data
                $this->supplierService->populatePersonnelUserData($data);

                if (strlen($data->p_ep_role) > 0) {
                    $isNotAppointedAdmin = false;
                }
            }

            $latestApplObj = $this->supplierService->getSmApplDetail($list[0]->latest_appl_id);

            /* Checking is MOF Expired Sync With Latest APPL */
            $isData->is_mof_error_activate = false;
            if ($latestApplObj && $latestApplObj->status_id == '20199' && ($latestApplObj->appl_type == 'R' || $latestApplObj->appl_type == 'N')) {
                $listCheckSupplierNotActivateMOF = $this->supplierService->checkSupplierMofExpiredNotActivate($list[0]->supplier_id);
                if (count($listCheckSupplierNotActivateMOF) > 0) {
                    $isData->is_mof_error_activate = true;
                }
            }

            $supplierDisciplinearyAction = $this->supplierService->getDetailSupplierDisciplinaryAction($list[0]->supplier_id);
            $listWorkFlow = $this->supplierService->getWorkFlowSupplierProcess($list[0]->supplier_id, $list[0]->latest_appl_id);
            $listSuppTrackDiary = $this->supplierService->getTrackingDiarySupplierByDocNo($list[0]->appl_no);
            $listAttachmentCancelReject = $this->supplierService->getListAttachmentRejectOrCancel($list[0]->latest_appl_id);
            $listRemarksCancelReject = $this->supplierService->getListRemarksRejectOrCancel($list[0]->latest_appl_id);
            $totalItems = $this->supplierService->getTotalItemsSupplier($list[0]->supplier_id);
            $sapVendorCode = $this->supplierService->getMainSapVendorCode($list[0]->ep_no);
            $suppMofStatus = $this->supplierService->getSupplierMofStatus($list[0]->supplier_id);
            $listSuppMofVirtCert = $this->supplierService->getSupplierMofVirtCert($list[0]->supplier_id);
            $listSuppPayment = $this->supplierService->getPaymentSuppliers($list[0]->supplier_id);
            $listinProgressSuppProcessAppl = $this->supplierService->getInProgressWorkFlowSupplierProcess($list[0]->supplier_id);
            $listInProgressSuppTrackDiary = null;
            $facilityInfoInProgress = null;

            if (count($listinProgressSuppProcessAppl) > 0) {
                $listInProgressSuppTrackDiary = $this->supplierService->getTrackingDiarySupplierByDocNo($listinProgressSuppProcessAppl[0]->appl_no);
                $facilityInfoInProgress = $this->supplierService->getFacilityInfo($listinProgressSuppProcessAppl[0]->appl_id);
            }

            $basicCompInfo = $this->supplierService->getBasicSupplierInfo($list[0]->latest_appl_id);
            $listApplRejectReason = $this->supplierService->getListApplRejectReason($list[0]->latest_appl_id);
            $listApplSectionReview = $this->supplierService->getListApplSectionReview($list[0]->latest_appl_id);
            $facilityInfo = $this->supplierService->getFacilityInfo($list[0]->latest_appl_id);

            if ($basicCompInfo && $basicCompInfo != null) {
                $basicCompInfo->is_with_federal = EpTrait::$YES_NO[$basicCompInfo->is_with_federal];
                $basicCompInfo->is_with_state = EpTrait::$YES_NO[$basicCompInfo->is_with_state];
                $basicCompInfo->is_with_statutory = EpTrait::$YES_NO[$basicCompInfo->is_with_statutory];
                $basicCompInfo->is_with_glc = EpTrait::$YES_NO[$basicCompInfo->is_with_glc];
                $basicCompInfo->is_with_others = EpTrait::$YES_NO[$basicCompInfo->is_with_others];

                $isData->is_name_hq_non_ascii = false;
                if (mb_check_encoding($basicCompInfo->company_name, 'ASCII') == false) {
                    $isData->is_name_hq_non_ascii = true;
                }

                $nonAsciiHqDetected = false;
                if (mb_check_encoding($basicCompInfo->address_1, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }
                if (mb_check_encoding($basicCompInfo->address_2, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }
                if (mb_check_encoding($basicCompInfo->address_3, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }

                $isData->is_address_hq_non_ascii = $nonAsciiHqDetected;
            }

            /** Check MOF expiry * */
            $list[0]->is_mof_expired = false;
            if ($list[0]->ma_exp_date) {
                $mofExpDate = Carbon::parse($list[0]->ma_exp_date);
                $now = Carbon::now();

                $list[0]->is_mof_expired = $mofExpDate < $now;
            }

            $listSupplierBranch = $this->supplierService->getListSupplierBranch($list[0]->latest_appl_id);
            $checkNonAscii = false;
            $checkBranchNameNonAscii = false;

            if (count($listSupplierBranch) > 0) {
                foreach ($listSupplierBranch as $branch) {
                    $branchNameNonAscii = false;
                    if (mb_check_encoding($branch->branch_name, 'ASCII') == false) {
                        $branchNameNonAscii = true;
                        $checkBranchNameNonAscii = true; //SetParent
                    }
                    $branch->is_branch_name_non_ascii = $branchNameNonAscii;

                    $nonAsciiDetected = false;
                    if (mb_check_encoding($branch->address_1, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    if (mb_check_encoding($branch->address_2, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    if (mb_check_encoding($branch->address_3, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    $branch->is_non_ascii = $nonAsciiDetected;
                    if ($branch->is_non_ascii == true) {
                        $checkNonAscii = true;
                    }

                    $branch->sap_vendor_code = '-';
                    $branchSapVendorCode = $this->supplierService->getMainSapVendorCodeByBranchCode($list[0]->ep_no, $branch->branch_code);
                    if ($branchSapVendorCode) {
                        $branch->sap_vendor_code = $branchSapVendorCode->sap_vendor_code;
                    }
                }
            }

            $isData->is_address_branch_non_ascii = $checkNonAscii;
            $isData->is_branch_name_non_ascii = $checkBranchNameNonAscii;

            $listSupplierBank = $this->supplierService->getListSupplierBank($list[0]->latest_appl_id);
            $hqGstInfo = $this->supplierService->getHqGstInfo($list[0]->supplier_id);
            $listSupplierCategoryCode = $this->supplierService->getListSupplierCategoryCode($list[0]->latest_appl_id);

            if (count($listSupplierCategoryCode) > 0) {
                foreach ($listSupplierCategoryCode as $cat) {
                    $cat->is_approved_by_po = EpTrait::$CATEGORY_IS_APPROVED[$cat->is_approved_by_po];
                    $cat->is_approved_by_ap = EpTrait::$CATEGORY_IS_APPROVED[$cat->is_approved_by_ap];
                    $cat->record_status = EpTrait::$RECORD_STATUS[$cat->record_status];
                }
            }

            $listPendingTransaction = array();
            // $listPendingTransaction = $this->supplierService->getListPendingTransaction($list[0]->supplier_id);
            $isPendingTransaction = false;

            /** Condition to show flag danger * */
            foreach ($listPendingTransaction as $data) {
                if ($data->get('total') > 0) {
                    $isPendingTransaction = true;
                    break;
                }
            }

            $this->results = [
                'listdata' => $list,
                'isNotAppointedAdmin' => $isNotAppointedAdmin,
                'totalItems' => $totalItems,
                'sapVendorCode' => $sapVendorCode,
                'basicCompInfo' => $basicCompInfo,
                'suppMofStatus' => $suppMofStatus,
                'listSuppTrackDiary' => $listSuppTrackDiary,
                'listSuppPayment' => $listSuppPayment,
                'listSuppMofVirtCert' => $listSuppMofVirtCert,
                'listSupplierBranch' => $listSupplierBranch,
                'listSupplierBank' => $listSupplierBank,
                'listSupplierCategoryCode' => $listSupplierCategoryCode,
                'listPendingTransaction' => $listPendingTransaction,
                'isPendingTransaction' => $isPendingTransaction,
                'hqGstInfo' => $hqGstInfo,
                'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                'listInProgressSuppTrackDiary' => $listInProgressSuppTrackDiary,
                'listWorkFlow' => $listWorkFlow,
                'listAttachmentCancelReject' => $listAttachmentCancelReject,
                'listRemarksCancelReject' => $listRemarksCancelReject,
                'listApplRejectReason' => $listApplRejectReason,
                'listApplSectionReview' => $listApplSectionReview,
                'isData' => $isData,
                'carian' => $searchValue,
                'type' => $searchType,
                'facilityInfo' => $facilityInfo,
                'facilityInfoInProgress' => $facilityInfoInProgress,
                'supplierDisciplinearyAction' => $supplierDisciplinearyAction
            ];
        } else {
            $this->results = [
                'listdata' => null,
                'listXml' => null,
                'type' => $searchType,
                'result' => 'notfound',
                'carian' => $searchValue
            ];
        }
    }

    /**
     * Generic method to load modal data
     * This can be extended for specific data types
     */
    public function loadModalData($type, $id = null, $additionalParams = [])
    {
        $this->selectedId = $id;
        $this->modalType = $type;
        $this->isLoading = true;
        $this->isModalOpen = true;
        $this->selectedData = $additionalParams;

        try {
            switch ($type) {
                case 'list-supplier-items':
                    $this->loadSupplierItemsModal($id);
                    break;
                case 'payment-history':
                    $this->loadPaymentHistoryModal($id);
                    // dd($this->modalData);
                    break;
                case 'list-history-appls':
                    break;
                case 'list-branches':
                    break;
                case 'list-banks':
                    break;
                case 'list-category-codes':
                    break;
                case 'total-pending-transactions':
                    break;
                case 'list-transactions':
                    break;
                case 'list-workflow-tracking-diary':
                    break;
                case 'list-personnel-address':
                default:
                    $this->modalData = [];
                    $this->modalTitle = 'Data Not Available';
                    break;
            }
        } catch (\Exception $e) {
            $this->modalData = [];
            $this->modalTitle = 'Error Loading Data';
            \Log::error("Modal data loading error: " . $e->getMessage());
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-supplier-modal');
    }

    /**
     * Close modal and reset state
     */
    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->selectedId = null;
        $this->selectedData = [];
        $this->modalTitle = '';
        $this->modalSubtitle = '';
        $this->modalType = '';
    }

    /**
     * Load supplier items modal data
     */
    private function loadSupplierItemsModal($supplierId)
    {
        $this->modalTitle = 'Supplier Items';
        $this->modalSubtitle = 'List of all items/products for this supplier';

        $this->modalData = $this->supplierService->getItemsSupplier($supplierId);
    }

    /**
     * Load supplier items modal data
     */
    private function loadPaymentHistoryModal($supplierId)
    {
        $this->modalTitle = 'Payment History';
        $this->modalSubtitle = 'List of all payment history for this supplier';

        $this->modalData = $this->supplierService->getPaymentSuppliers($supplierId);
        ;
    }

    public function render()
    {
        // If we have results, extract them from the array
        if (!empty($this->results)) {
            // Add modal data to the results
            $this->results['modalData'] = $this->modalData;
            $this->results['modalTitle'] = $this->modalTitle;
            $this->results['modalSubtitle'] = $this->modalSubtitle;
            $this->results['modalType'] = $this->modalType;
            $this->results['isModalOpen'] = $this->isModalOpen;
            $this->results['isLoading'] = $this->isLoading;

            return view('livewire.supplier.supplier-search-technical', $this->results);
        }

        // If no search has been performed yet
        return view('livewire.supplier.supplier-search-technical', [
            'listdata' => null,
            'result' => null,
            'carian' => $this->supplierSearchForm->search ?? '',
            'modalData' => $this->modalData,
            'modalTitle' => $this->modalTitle,
            'modalSubtitle' => $this->modalSubtitle,
            'modalType' => $this->modalType,
            'isModalOpen' => $this->isModalOpen,
            'isLoading' => $this->isLoading
        ]);
    }
}