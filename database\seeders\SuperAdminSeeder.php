<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $role = Role::create([
            'role_code' => 'super_admin',
            'role_name' => 'Super Administrator',
            'description' => 'Has full access to all system features'
        ]);

        $routes = [
            // Format: 'route_name' => ['action1', 'action2', ...]
            'settings' => ['create', 'read', 'update', 'delete'],
            'supplier' => ['read'],
            'supplier-detail' => ['read'],
        ];

        $permissions = [];

        foreach ($routes as $routeName => $actions) {
            foreach ($actions as $action) {
                $permissionName = $routeName . '_' . $action;
                $permission = Permission::create([
                    'name' => $permissionName,
                    'action' => $action,
                    'route_name' => $routeName,
                    'description' => ucfirst($action) . ' access to ' . $routeName . ' page'
                ]);
                $permissions[] = $permission->id;
            }
        }

        // Attach all permissions to the super_admin role
        $role->permissions()->attach($permissions);

        // Create the epssadmin user
        $user = User::create([
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'name' => 'Super Admin',
            'user_name' => 'epssadmin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'), // Change this for production!
            'is_ldap' => false,
            'is_crm' => false,
        ]);

        // Attach the super_admin role to the epssadmin user
        $user->roles()->attach($role->id);
    }
}