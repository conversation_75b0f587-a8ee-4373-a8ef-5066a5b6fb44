<?php

namespace App\Livewire\Module\QuotationTender;

use App\Livewire\Forms\QuotationTender\SummarySearchForm;
use App\Services\SourcingService;
use Auth;
use Livewire\Component;
use Livewire\Attributes\Url;
use Carbon\Carbon;

class Summary extends Component
{
    protected SourcingService $sourcingService;

    public SummarySearchForm $summarySearchForm;

    // Add public properties to store the data
    #[Url]
    public $searchValue;
    public $detailQt = null;
    public $detailZonal = null;
    public $detailQt2 = null;
    public $listDetailQt22 = null;
    public $getQtfinal = null;
    public $getBsvfinal = null;
    public $getPrevious = null;
    public $qtNo = null;

    public function boot(SourcingService $sourcingService)
    {
        $this->sourcingService = $sourcingService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($this->searchValue) {
            $this->summarySearchForm->search = $this->searchValue;
            $this->searchQuotationTenderSummary();
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-qt-bsv-table');
    }

    public function searchQuotationTenderSummary()
    {
        $this->summarySearchForm->validate();
        $this->searchValue = $this->summarySearchForm->search;

        $this->getBSVdetails($this->searchValue);
    }

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.qt.summary', $action);
    }

    private function getBSVdetails($searchValue)
    {
        $docNo = $searchValue;
        $this->detailQt = null;
        $this->detailZonal = null;
        $this->detailQt2 = null;
        $this->listDetailQt22 = null;
        $this->qtNo = null;

        $getQtNo = null;
        $getqt = null;
        $getqt1 = null;
        $getqt2 = null;
        $getqt11 = null;
        $getqtprevious = null;
        $getbsv1 = null;
        $getbsv2 = null;
        $this->getBsvfinal = null;
        $this->getQtfinal = null;
        $this->getPrevious = null;

        if (strpos($docNo, 'LA') !== false) {
            //LA daripada QT
            $getQtNo = $this->sourcingService->getQTNoFromLOA($docNo);
            if ($getQtNo)
                $this->qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'LI') !== false) {
            $getQtNo = $this->sourcingService->getQTNoFromLOI($docNo);
            if ($getQtNo)
                $this->qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'BD') !== false) {
            $getQtNo = $this->sourcingService->getQTNoFromBIDDING($docNo);
            if ($getQtNo)
                $this->qtNo = $getQtNo[0]->doc_no;
        } else {
            $this->qtNo = $docNo;
        }

        if ($this->qtNo && $this->qtNo != null) {
            $listDetailQt = [];
            if (strpos($this->qtNo, 'QT') !== false) {
                $listDetailQt = $this->sourcingService->getDetailQuatationTenderSummary($this->qtNo);
            } else if (strpos($this->qtNo, 'QM') !== false) {
                $listDetailQt = $this->sourcingService->getDetailQuatationTenderSummary($this->qtNo);
            } else if (strpos($this->qtNo, 'X') !== false) {
                $listDetailQt = $this->sourcingService->getDetailQuatationTenderSummary($this->qtNo);
            } else if (strpos($this->qtNo, 'DL') !== false) {
                $listDetailQt = $this->sourcingService->getDetailQuatationTenderDL($this->qtNo);
            }
            $listDetailListZonal = $this->sourcingService->getDetailZonalQTSummary($this->qtNo);
            $this->listDetailQt22 = $this->sourcingService->getDetailQuatationTenderBSV($this->qtNo);
            $getqt = (array) $this->sourcingService->getNewQTNo($docNo);

            if (count($listDetailQt) > 0) {
                $this->detailQt = $listDetailQt[0];
            }
            //akmal add details zonal
            if (count($listDetailListZonal) > 0) {
                $this->detailZonal = $listDetailListZonal;
            }
            if (count($this->listDetailQt22) > 0) {
                $this->detailQt2 = $this->listDetailQt22[0];
            }
            if (count($this->listDetailQt22) > 0) {
                $getbsv1 = (array) $this->listDetailQt22[0];
                $this->getBsvfinal = $getbsv1['bsv_date'];
            }
            if (count($getqt) > 0) {
                $getqt1 = (array) $getqt[0];
                $getqt2 = $this->sourcingService->CheckNewGenerateQTNo($getqt1['qt_id']);
                if (count($getqt2) > 0) {
                    $this->getQtfinal = $getqt2[0];
                }
            }
            if (count($getqt) > 0) {
                $getqt11 = (array) $getqt[0];
                $getqtprevious = $this->sourcingService->CheckPreviousQTno($getqt11['qt_id']);
                if (count($getqtprevious) > 0) {
                    $this->getPrevious = $getqtprevious[0];
                }
            }
        }
    }

    public function render()
    {
        return view('livewire.quotation-tender.summary', [
            'newqt' => $this->getQtfinal,
            'getbsv' => $this->getBsvfinal,
            'previousqt' => $this->getPrevious,
            'qtinfo' => $this->detailQt,
            'kodzonal' => $this->detailZonal,
            'listdata22' => $this->listDetailQt22,
            'carian' => $this->searchValue
        ]);
    }
}