<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Request;

class MenuService
{
    protected $menuConfig;
    protected $currentRoute;

    public function __construct()
    {
        $this->menuConfig = config('menu.items');
        $this->currentRoute = Request::url();
    }

    public function getMenu()
    {
        $menu = [];

        foreach ($this->menuConfig as $key => $item) {
            $processedItem = $this->processMenuItem($item, $key);
            if ($processedItem) {
                $menu[] = $processedItem;
            }
        }

        return $menu;
    }

    protected function processMenuItem($item, $key, $parent = null)
    {
        // Skip if user doesn't have permission
        if (!empty($item['permission']) && !Auth::user()->canAccessRoute($item['permission'], 'read')) {
            return null;
        }

        $processedItem = [
            'type' => $item['type'] ?? 'item',
            'title' => $item['title'],
            'id' => $key,
        ];

        // Add icon if present
        if (isset($item['icon'])) {
            $processedItem['icon'] = $item['icon'];
        }

        // Add hidden flag if present
        if (isset($item['hidden'])) {
            $processedItem['hidden'] = $item['hidden'];
        }

        // Build URL from route if present
        if (isset($item['route'])) {
            $query = $item['query'] ?? [];
            $processedItem['url'] = route($item['route'], $query);
            $processedItem['active'] = $this->isActive($processedItem['url']);

            // Check if tab parameter matches current request
            if (!empty($query['tab'])) {
                $requestTab = request()->query('tab');
                $itemTab = $query['tab'];

                // For default tabs (when tab is not in URL but item is the default)
                if ($requestTab === null && isset($item['is_default_tab']) && $item['is_default_tab']) {
                    // Consider active if URL matches without considering tab
                } else {
                    // Override active status based on tab parameter
                    $processedItem['active'] = $processedItem['active'] &&
                        ($requestTab === $itemTab || ($requestTab === null && $itemTab === ''));
                }
            }
        }

        // Add disabled flag if present
        if (isset($item['disabled'])) {
            $processedItem['disabled'] = $item['disabled'];
        }

        // Process child items if present
        if (isset($item['items']) && is_array($item['items'])) {
            $childItems = [];
            $hasAccessibleChildren = false;

            foreach ($item['items'] as $childKey => $childItem) {
                $processedChild = $this->processMenuItem($childItem, $childKey, $key);
                if ($processedChild) {
                    $childItems[] = $processedChild;
                    $hasAccessibleChildren = true;

                    // If any child is active, mark parent as active too
                    if ($processedChild['active'] ?? false) {
                        $processedItem['active'] = true;
                    }
                }
            }

            // Only add items array if there are accessible children
            if ($hasAccessibleChildren) {
                $processedItem['items'] = $childItems;
            } else if ($item['type'] === 'section') {
                // Don't show sections with no accessible children
                return null;
            }
        }

        return $processedItem;
    }

    protected function isActive($url)
    {
        // 1. Check for exact URL match
        if ($url == $this->currentRoute) {
            return true;
        }

        // 2. Get the base URL without domain
        $urlPath = parse_url($url, PHP_URL_PATH);
        $currentPath = parse_url($this->currentRoute, PHP_URL_PATH);

        // 3. Check if current path starts with the menu item path
        if (
            $urlPath && $currentPath &&
            ($currentPath === $urlPath ||
                strpos($currentPath, $urlPath . '/') === 0)
        ) {
            return true;
        }

        // 4. Use Laravel's Request::is for pattern matching
        $relativePath = ltrim($urlPath, '/');
        return Request::is($relativePath) || Request::is($relativePath . '/*');
    }
}