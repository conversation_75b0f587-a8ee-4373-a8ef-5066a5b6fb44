<?php

namespace App\Livewire\Project;

use Livewire\Component;

class CRM extends Component
{
    public $activeTab;

    public function mount()
    {
        $this->activeTab = request()->query('tab');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        return redirect()->route('crm', ['tab' => $tab]);
    }

    public function render()
    {
        return view('livewire.project.crm.index');
    }
}