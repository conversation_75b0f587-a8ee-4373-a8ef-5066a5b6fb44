<?php

namespace App\Livewire\Module\BpmApi;

use Auth;
use Livewire\Component;
use App\Livewire\Forms\Bpm\FindTaskSearchForm;
use App\Services\BpmApiService;
use Log;

class FindTask extends Component
{
    public FindTaskSearchForm $form;
    public $results = [];
    public $errorMessage = null;
    public $module = null;
    public $doc_no = null;
    public $created_date = null;

    protected BpmApiService $bpmApiService;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('bpm.bpm-api.find-task', $action);
    }

    public function boot(BpmApiService $bpmApiService)
    {
        $this->bpmApiService = $bpmApiService;
    }

    public function mount()
    {
        if($this->module) {
            $this->form->module = $this->module;
        }
        if($this->doc_no) {
            $this->form->doc_no = $this->doc_no;
        }
        if($this->created_date) {
            $this->form->created_date = $this->created_date;
        }
        
        $this->findTasks();
    }

    public function findTasks()
    {
        $this->form->validate();

        $results = collect();
        try {
            $doc_no = $this->form->doc_no;
            $module = $this->form->module;
            $created_date = $this->form->created_date;
            $data = collect([
                "doc_no" => $doc_no,
                "module" => $module,
                "created_date" => $created_date]);
            $results = $this->bpmApiService->apiServices('task', $data);

            if ($results["status"] == 'Success') {
                if(count($results["result"]) > 0) {
                    foreach ($results["result"] as $key => $row) {
                        $listComposite = explode("*", $row['compositeDN']);
                        $results[$key]['composite'] = $listComposite[0];
                    }
                }
            }
            $this->results = $results;
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }

    }

    public function render()
    {
        return view('livewire.bpm-api.find-task', [
            'tasks' => $this->results,
        ]);
    }
}
