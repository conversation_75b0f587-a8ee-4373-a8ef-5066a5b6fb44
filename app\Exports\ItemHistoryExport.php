<?php

namespace App\Exports;

use App\Invoice;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ItemHistoryExport implements FromArray, WithHeadings, WithStyles, WithColumnFormatting, ShouldAutoSize
{
    protected $itemHistory;

    public function __construct(array $itemHistory)
    {
        $this->itemHistory = $itemHistory;
    }

    public function headings(): array
    {
        return [
            'TARIKH',
            'IC NO.',
            'NAME',							
            'DOC NO',
            'PRODUCT NAME',
            'COMPANY NAME',
            'EP NO',
            'STATUS'
        ];
    }

    public function array(): array
    {
        return $this->itemHistory;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1    => ['font' => ['bold' => true]],
        ];
    }
    
    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER,
        ];
    }

    // public function collection($data)
    // {
    //     return new Collection([
    //         [1, 2, 3],
    //         [4, 5, 6]
    //     ]);
    // }
}