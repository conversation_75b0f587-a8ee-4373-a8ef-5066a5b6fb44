<?php

namespace App\Livewire\Module\RevenueReport;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;

class PendingTransaction extends Component
{
    protected RevenueService $RevenueService;

    // Public properties to store data
    public $listdata = [];
    public $errorMessage = null;

    public function boot(RevenueService $RevenueService)
    {
        $this->revenueService = $RevenueService;
    }

    public function mount()
    {
        $yesterday = Carbon::yesterday()->format('Y');
        $list = $this->revenueService->cmsListPendingTransactionStatistic($yesterday);
        $this->listdata = $list;
    }

    public function render()
    {
        return view('livewire.revenue-report.pending-transaction', [ 
            'listdata' => $this->listdata
        ]);
    }
}