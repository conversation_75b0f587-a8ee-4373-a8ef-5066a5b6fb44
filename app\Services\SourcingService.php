<?php

namespace App\Services;

use DB;

class SourcingService
{
    public function getDashboardQtListStatusClosedTooEarly()
    {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT   a.qt_id, a.qt_no, b.status_id, c.status_name, b.created_date,
                    a.publish_date, a.closing_date, b.created_by
               FROM sc_qt a, sc_workflow_status b, pm_status_desc c
              WHERE a.qt_id = b.doc_id
                AND b.status_id = c.status_id
                AND b.is_current = 1
                AND c.language_code = 'ms'
                AND b.status_id > (60009)
                AND b.status_id NOT IN (60015)
                AND b.doc_type = 'QT'
                AND TRUNC (a.closing_date) = TRUNC (SYSDATE)
                AND (b.created_date > SYSDATE - 1 AND b.created_date < SYSDATE + 1)
                AND TO_CHAR (b.created_date, 'hh24:mm') < '12:00'
           ORDER BY b.created_date"
        );

        return $results;
    }

    public function getQTNoFromLOA($LoaNo)
    {
        // Log::info(__METHOD__." >> ".$LoaNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "
        select c.qt_no
        from sc_loa a,sc_loi_loa b, sc_qt c
        where a.loi_loa_id = b.loi_loa_id
        and b.doc_id = c.qt_id
        and a.loa_no = ?
         ",
            array($LoaNo)
        );
        return $results;
    }

    public function getQTNoFromLOI($LoiNo)
    {
        // Log::info(__METHOD__." >> ".$LoiNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select c.qt_no
        from sc_loi a,sc_loi_loa b, sc_qt c
        where a.loi_loa_id = b.loi_loa_id
        and b.doc_id = c.qt_id
        and a.loi_no = ?
         ",
            array($LoiNo)
        );
        return $results;
    }

    public function getQTNoFromBIDDING($BidNo)
    {
        // Log::info(__METHOD__." >> ".$BidNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select a.doc_no
        from SC_BID a, SC_BID_SCHEDULE b, sc_qt c
        where a.bid_id = b.bid_id
        and c.qt_no = a.doc_no
        and b.bid_no = ?
         ",
            array($BidNo)
        );
        return $results;
    }

    public function getDetailQuatationTenderSummary($docNo)
    {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
        SELECT sq.file_no, sq.is_panel,sq.parent_qt_id,
        decode ((sq.is_panel), '0', 'NO  ', '1', 'Panel Only  ') as panel,
        sq.is_panel as checkpanel,
        decode ((sq.fulfilment_type_id), '148', 'One Off', '149', 'Periodic Schedule','150', 'Periodic As and When Basic') as ff_type,
        decode ((sq.evaluate_by), 'B', 'Pakej & Item', 'I', 'Item','P', 'Pakej') as item_or_pakej,
        decode ((sq.qt_type), 'O', 'Open', 'R', 'Restricted','I', 'International Tender')as jenis_qt,      
        decode ((sq.evaluation_type), '1', '1 Tier', '2', '2 Tier')as evaluation_type2,
        decode ((sq.is_zonal), '1', 'YES ', '0', 'NO ')as is_zonal,
        decode ((sq.open_for), 'A', 'All', 'B', 'Bumiputra')as openfor2,
        decode ((sq.IS_BSV_REQ), '1', 'YES', '0', 'NO')as brefingsitevisit,
        decode ((sq.record_status), '1', 'Active', '0', 'Inactive')as status_supplier,
        sq.qt_no, sq.publish_date,sq.qt_validity_period, sq.publish_period,
        sq.closing_date,
         CASE
             WHEN sq.closing_date <= SYSDATE
                THEN 'iklanexpired'
             ELSE ''
          END AS checkvalidityiklan,
        sq.qt_id, sq.contract_duration,
        sq.evaluation_type,
        sq.proposal_validity_end_date,
        CASE
             WHEN sq.proposal_validity_end_date <= SYSDATE
                THEN 'qtexpired'
             ELSE ''
          END AS checkvalidityqt,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_mode_id = pd.parameter_id AND pd.language_code = 'en') AS procurement,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_category_id = pd.parameter_id AND pd.language_code = 'en') AS TYPE,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.PROCUREMENT_TYPE_CAT_ID = pd.parameter_id AND pd.language_code = 'en') AS category,
        vm.org_code AS ministry_code, vm.org_name AS ministry_name,
        vpp.org_code AS pp_code, vpp.org_name AS pp_name,
        vj.org_code AS kptj_code, vj.org_name AS kptj_name,
        vp.org_code AS ptj_code, vp.org_name AS ptj_name, 
        vp1.ORG_CODE as for_ptj_code, vp1.ORG_NAME as for_ptj_name,
        sd.status_id,
        sd.status_name, sq.qt_title,
        (SELECT NVL (MAX (s.state_name), 'N/A')
         FROM pm_state s WHERE s.state_id IN (SELECT adr.state_id FROM pm_address adr WHERE adr.address_id IN (
             SELECT pat.address_id FROM pm_address_type pat WHERE pat.address_type = 'B'
             AND record_status = 1 AND pat.org_profile_id = sq.org_profile_id))) AS state_name,
        pu.login_id, pu.user_name
 FROM   sc_qt sq, pm_org_validity vp, pm_org_profile pp, pm_org_validity vj,
        pm_org_profile pj, pm_org_validity vpp, pm_org_profile ppp, pm_org_validity vm,
        pm_org_profile pm, pm_parameter mtr, sc_workflow_status s,   pm_status_desc sd,pm_user pu,
        pm_org_validity vp1       
 WHERE  sq.owner_org_profile_id = vp.org_profile_id
        AND vp.org_profile_id = pp.org_profile_id
        AND sq.ORG_PROFILE_ID = vp1.ORG_PROFILE_ID
        AND pp.parent_org_profile_id = vj.org_profile_id
        AND vj.org_profile_id = pj.org_profile_id
        AND pj.parent_org_profile_id = vpp.org_profile_id
        AND vpp.org_profile_id = ppp.org_profile_id
        AND ppp.parent_org_profile_id = vm.org_profile_id
        AND vm.org_profile_id = pm.org_profile_id
        AND pp.org_type_id = mtr.parameter_id
        AND sq.created_by = pu.user_id
        AND vp.record_status = 1
        AND vp1.record_status = 1
        AND vj.record_status = 1
        AND vpp.record_status = 1
        AND vm.record_status = 1
        AND vp.exp_date > SYSDATE
        AND sq.qt_id = s.doc_id
        AND s.status_id = sd.status_id
        AND sd.language_code = 'en'
        AND s.created_date IN (SELECT MAX (x.created_date) FROM sc_workflow_status x
           WHERE sq.qt_id = x.doc_id AND x.doc_type in ('QT','CMT','TEC','OC','FEC','EC','BD'))
       AND sq.qt_no = ?
 GROUP BY sq.file_no, sq.is_panel,sq.parent_qt_id, sq.fulfilment_type_id,sq.qt_title, sq.publish_date, sq.is_zonal,
          sq.qt_validity_period, sq.publish_period,sq.qt_no,sq.qt_id,sq.contract_duration,
          sq.evaluation_type, sq.is_bsv_req, sd.status_name, sd.status_id,sq.closing_date, sq.qt_id, sq.org_profile_id,
          sq.proposal_validity_end_date, sq.procurement_mode_id, sq.procurement_category_id, sq.PROCUREMENT_TYPE_CAT_ID,
          vp.org_name, vp.org_code, vj.org_name, vj.org_code, vpp.org_name, vpp.org_code, vm.org_name, vm.org_code,
          pu.login_id, pu.user_name,sq.EVALUATE_BY, sq.QT_TYPE,sq.EVALUATION_TYPE,sq.IS_BSV_REQ,sq.RECORD_STATUS, 
          sq.org_profile_id, vp1.ORG_CODE, vp1.ORG_NAME, sq.open_for
         ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    public function getDetailQuatationTenderDL($docNo)
    {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
    SELECT distinct sq.file_no, sq.is_panel,sq.parent_qt_id,
       decode ((sq.is_panel), '0', 'Normal  ', '1', 'Panel Only  ') as panel,
       sq.is_panel as checkpanel,
       decode ((sq.fulfilment_type_id), '148', 'One Off', '149', 'Periodic Schedule','150', 'Periodic As and When Basic') as ff_type,
       decode ((sq.evaluate_by), 'B', 'Pakej & Item', 'I', 'Item','P', 'Pakej') as item_or_pakej,
       decode ((sq.qt_type), 'O', 'Open/Terbuka', 'R', 'Restricted','I', 'International Tender')as jenis_qt,
       decode ((sq.evaluation_type), '1', '1 Tier', '2', '2 Tier')as evaluation_type2,
       decode ((sq.open_for), 'A', 'All', 'B', 'Bumiputra')as openfor2,
       decode ((sq.IS_BSV_REQ), '1', 'YES', '0', 'NO')as brefingsitevisit,
       decode ((sq.record_status), '1', 'Active', '0', 'Inactive')as status_supplier,
       sq.qt_no, sq.publish_date,
       sq.closing_date,
        CASE
            WHEN sq.closing_date <= SYSDATE
               THEN 'iklanexpired'
            ELSE ''
         END AS checkvalidityiklan,
       sq.qt_id,sq.contract_duration, sq.evaluation_type,
       sq.proposal_validity_end_date,
       CASE
            WHEN sq.proposal_validity_end_date <= SYSDATE
               THEN 'qtexpired'
            ELSE ''
         END AS checkvalidityqt,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_mode_id = pd.parameter_id AND pd.language_code = 'en') AS procurement,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_category_id = pd.parameter_id AND pd.language_code = 'en') AS TYPE,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.PROCUREMENT_TYPE_CAT_ID = pd.parameter_id AND pd.language_code = 'en') AS category,
       vm.org_code AS ministry_code, vm.org_name AS ministry_name,
       vpp.org_code AS pp_code, vpp.org_name AS pp_name,
       vj.org_code AS kptj_code, vj.org_name AS kptj_name,
       vp.org_code AS ptj_code, vp.org_name AS ptj_name, 
       vp1.ORG_CODE as for_ptj_code, vp1.ORG_NAME as for_ptj_name,
       sd.status_id,sd.status_name,
       sq.qt_title,
      (SELECT NVL (MAX (s.state_name), 'N/A')
       FROM pm_state s WHERE s.state_id IN (SELECT adr.state_id FROM pm_address adr WHERE adr.address_id IN (
            SELECT pat.address_id FROM pm_address_type pat WHERE pat.address_type = 'B'
           AND record_status = 1 AND pat.org_profile_id = sq.org_profile_id))) AS state_name,
       pu.login_id, pu.user_name
    FROM 
       sc_qt sq, pm_org_validity vp, pm_org_profile pp, pm_org_validity vj,
       pm_org_profile pj, pm_org_validity vpp, pm_org_profile ppp, pm_org_validity vm,
       pm_org_profile pm, pm_parameter mtr, sc_workflow_status s,  pm_status_desc sd,pm_user pu,
       pm_org_validity vp1   
    WHERE  sq.owner_org_profile_id = vp.org_profile_id(+)
       AND vp.org_profile_id = pp.org_profile_id(+)
       AND sq.ORG_PROFILE_ID = vp1.ORG_PROFILE_ID
       AND pp.parent_org_profile_id = vj.org_profile_id(+)
       AND vj.org_profile_id = pj.org_profile_id(+)
       AND pj.parent_org_profile_id = vpp.org_profile_id(+)
       AND vpp.org_profile_id = ppp.org_profile_id(+)
       AND ppp.parent_org_profile_id = vm.org_profile_id(+)
       AND vm.org_profile_id = pm.org_profile_id(+)
       AND pp.org_type_id = mtr.parameter_id(+)
       AND sq.created_by = pu.user_id(+)
       AND vp.record_status = 1
       AND vp1.record_status = 1
       AND vj.record_status = 1
       AND vpp.record_status = 1
       AND vm.record_status = 1
       AND vp.exp_date > SYSDATE
       AND sq.qt_id = s.doc_id(+)
       AND s.status_id = sd.status_id(+)
       AND sd.language_code = 'en'
      -- AND s.created_date IN (SELECT MAX (x.created_date) FROM sc_workflow_status x
      -- WHERE sq.qt_id = x.doc_id AND x.doc_type in ('QT','CMT','TEC','OC','FEC','EC','BD'))
      AND sq.qt_no = ?
         ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    public function getDetailZonalQTSummary($docNo)
    {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
        SELECT distinct szd.zone_name,
        loc.state_id || ' - ' || st.STATE_NAME state,
        loc.district_id || ' - ' || ds.DISTRICT_NAME district,
        loc.division_id || ' - ' || div.division_name division
   FROM sc_qt qt,
        sc_qt_supplier_criteria qtc,
        tpl_spec_zone sz,
        tpl_spec_zone_dtl szd,
        tpl_spec_loc loc,
        pm_state st,
        pm_division div,
        pm_district ds
  WHERE qt.qt_id = qtc.qt_id
    AND qtc.qt_supplier_criteria_id = sz.doc_id
    AND sz.spec_zone_id = szd.spec_zone_id
    AND szd.spec_zone_dtl_id = loc.doc_id
    AND loc.division_id = div.division_id(+)
    and loc.STATE_ID = st.STATE_ID (+)
    and loc.DISTRICT_ID= ds.DISTRICT_ID(+)
    AND loc.doc_type = 'ZON'
    AND sz.doc_type = 'QTZ'
    and qt.qt_no=?
ORDER BY  zone_name
    ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    public function getDetailQuatationTenderBSV($docNo)
    {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "
    SELECT
        s.company_name, e.mof_no,
        decode ((g.invitation_type), 'I', 'Invite', 'S', 'Schedular', 'N/A')as jenis_invitation,
        decode ((g.disqualified_stage), 'B', 'Failed To Attend', 'C', 'Not Fulfill The Criteria')as disqualified_stage,
        decode ((c.bsv_type), 'S', 'Site Visit', 'B', 'Briefing','A', 'Site Visit & Briefing','N/A')as bsv_type2,
        h.zone_name zonename, --UTARA, TENGAH, SELATAN, TIMUR
        --decode ((h.zone_name), 'SEMENANJUNG', 'Semenanjung', 'SABAH & SARAWAK', 'Sabah&Sarawak', 'N/A')as zonename,
        c.bsv_desc,
        decode ((d.is_attended), '1', 'Yes', '0', 'No','')as is_attended,
        c.bsv_date,
        decode ((d.is_post_registered), '1', 'Yes', '0', 'No','')as ispost_registered,
        d.qt_approval_request_id,
        decode ((f.approver_action_id), '479', 'Approved', '480', 'Rejected')as approver_action_id,
        case when g.disqualified_stage = 'B'
        and d.is_post_registered = '1'
        and d.qt_approval_request_id is not null
        and f.approver_action_id ='479'
        then '!'
        when g.disqualified_stage = 'C'
        and d.is_post_registered = '1'
        and d.qt_approval_request_id is not null
        and f.approver_action_id ='479'
        then '!' end as np,
        k.user_name,
        f.CREATED_DATE as Approver_createdate,
        f.CHANGED_DATE as Approver_changedate
    FROM sc_qt a,
         sc_qt_bsv b,
         sc_qt_bsv_dtl c,
         sc_qt_bsv_registration d,
         sm_mof_account e,
         sc_qt_supplier g,
         sc_qt_approval_request f,
         tpl_spec_zone_dtl h,
         pm_user k,
         sm_supplier s
    WHERE a.qt_id = b.qt_id
        AND a.qt_id = g.qt_id
        AND b.qt_bsv_id = c.qt_bsv_id
        AND c.qt_bsv_dtl_id = d.qt_bsv_dtl_id
        AND d.supplier_id = e.supplier_id
        AND g.supplier_id = e.supplier_id
        AND g.supplier_id = s.supplier_id
        AND f.approver_id = k.user_id (+)
        AND d.qt_approval_request_id = f.qt_approval_request_id(+)
        AND b.SPEC_ZONE_DTL_ID = h.SPEC_ZONE_DTL_ID (+)
        AND a.qt_no = ?
    ORDER BY disqualified_stage desc, e.mof_no
             ",
            array($docNo)
        );
        return $results;
    }

    public function getNewQTNo($qtno)
    {
        // Log::info(__METHOD__." >> ".$qtno);
        $query = "
         select qt_id from sc_qt where qt_no = ?
         ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($qtno));
    }

    public function CheckNewGenerateQTNo($qtid)
    {
        // Log::info(__METHOD__." >> qtid :: ".$qtid);
        $query = "
         select qt_no as qtno2, qt_id  from sc_qt where parent_qt_id = ?
         ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($qtid));
    }

    public function CheckPreviousQTno($qtno)
    {
        // Log::info(__METHOD__." >> ".$qtno);
        $query = "
         select qt_id as pqtid, qt_no as pqtno from sc_qt where qt_id in (select parent_qt_id from sc_qt where qt_id = ?)
         ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($qtno));
    }
}