<?php

return [
    // Define all menu items in a structured way
    'items' => [
        'home' => [
            'type' => 'item',
            'title' => 'Home',
            'icon' => 'ki-home',
            'route' => 'home',
            'permission' => null, // No permission required, always visible
        ],
        'crm' => [
            'type' => 'item',
            'title' => 'CRM',
            'icon' => 'ki-information',
            'route' => 'crm',
            'permission' => 'crm',
            'items' => [
                'cs-cases' => [
                    'title' => 'CRM - CS Cases',
                    'route' => 'crm',
                    'permission' => 'crm.cs-cases',
                    'query' => ['tab' => 'cs-cases'],
                ],
                'cs-pending-input' => [
                    'title' => 'CRM - CS Pending Input',
                    'route' => 'crm',
                    'permission' => 'crm.cs-pending-input',
                    'query' => ['tab' => 'cs-pending-input'],
                ],
                'poms-talkdesk' => [
                    'title' => 'POMS - Talkdesk',
                    'route' => 'crm',
                    'permission' => 'crm.poms-talkdesk',
                    'query' => ['tab' => 'poms-talkdesk'],
                ],
            ],
        ],
        'eperolehan' => [
            'type' => 'section',
            'title' => 'ePerolehan',
            'icon' => 'ki-document',
            'route' => 'ep',
            'permission' => 'ep',
            'items' => [
                'qt' => [
                    'type' => 'section',
                    'title' => 'Quotation Tender',
                    'route' => 'ep',
                    'query' => ['tab' => 'qt'],
                    'items' => [
                        'qt-dashboard' => [
                            'title' => 'Dashboard',
                            'route' => 'ep',
                            'permission' => 'ep.qt.dashboard',
                            'query' => ['tab' => 'qt-dashboard'],
                        ],
                        'summary' => [
                            'title' => 'Summary',
                            'route' => 'ep',
                            'permission' => 'ep.qt.summary',
                            'query' => ['tab' => 'qt-summary'],
                        ],
                    ],
                ],
                'profile' => [
                    'type' => 'section',
                    'title' => 'Profile',
                    'route' => 'ep',
                    'query' => ['tab' => 'profile'],
                    'items' => [
                        'my-identity' => [
                            'title' => 'MyIdentity',
                            'route' => 'ep',
                            'permission' => 'ep.profile.my-identity',
                            'query' => ['tab' => 'my-identity'],
                        ],
                        'user-login' => [
                            'title' => 'User Login',
                            'route' => 'ep',
                            'permission' => 'ep.profile.user-login',
                            'query' => ['tab' => 'user-login'],
                        ],
                        'find-organization' => [
                            'title' => 'Find Organization',
                            'route' => 'ep',
                            'permission' => 'ep.profile.organization',
                            'query' => ['tab' => 'organization'],
                        ],
                    ],
                ],
                'supplier' => [
                    'type' => 'section',
                    'title' => 'Supplier',
                    'route' => 'ep',
                    'query' => ['tab' => 'supplier'],
                    'items' => [
                        'find-supplier-technical' => [
                            'title' => 'Find Supplier',
                            'route' => 'ep',
                            'permission' => 'ep.supplier-technical',
                            'query' => ['tab' => 'supplier-technical'],
                        ],
                        'find-supplier-cs' => [
                            'title' => 'Find Supplier',
                            'route' => 'ep',
                            'permission' => 'ep.supplier-cs',
                            'query' => ['tab' => 'supplier-cs'],
                        ],
                    ],
                ],
                'tracking-diary' => [
                    'title' => 'Tracking Diary',
                    'route' => 'ep',
                    'permission' => 'ep.tracking-diary',
                    'query' => ['tab' => 'tracking-diary'],
                ],
                'item' => [
                    'type' => 'section',
                    'title' => 'Item',
                    'route' => 'ep',
                    'query' => ['tab' => 'item'],
                    'items' => [
                        'find-uom' => [
                            'title' => 'Find UOM',
                            'route' => 'ep',
                            'permission' => 'ep.item.uom',
                            'query' => ['tab' => 'uom'],
                        ],
                        'find-items' => [
                            'title' => 'Find Items',
                            'route' => 'ep',
                            'permission' => 'ep.item.item-code',
                            'query' => ['tab' => 'item-code'],
                        ],
                        'find-unspsc-items' => [
                            'title' => 'Find UNSPSC Items',
                            'route' => 'ep',
                            'permission' => 'ep.item.unspsc-item',
                            'query' => ['tab' => 'unspsc-item'],
                        ],
                        'find-supplier-item' => [
                            'title' => 'Find Supplier\'s Item',
                            'route' => 'ep',
                            'permission' => 'ep.item.supplier-item',
                            'query' => ['tab' => 'supplier-item'],
                        ],
                        'item-task-history' => [
                            'title' => 'Find Item Task History',
                            'route' => 'ep',
                            'permission' => 'ep.item.item-task-history',
                            'query' => ['tab' => 'item-history'],
                        ],
                    ],
                ],
            ],
        ],
        'report' => [
            'type' => 'item',
            'title' => 'REPORT - REVENUE',
            'icon' => 'ki-graph-up',
            'route' => 'report',
            'permission' => 'report',
            'items' => [
                'today-trans' => [
                    'title' => 'Today Transaction',
                    'route' => 'report',
                    'permission' => 'report.revenue-report.today-trans',
                    'query' => ['tab' => 'today-trans'],
                ],
                'accumulative' => [
                    'title' => 'Accumulative YTD & MTD',
                    'route' => 'report',
                    'permission' => 'report.revenue-report.accumulative',
                    'query' => ['tab' => 'accumulative'],
                ],
                'daily-summary' => [
                    'title' => 'Daily Summary',
                    'route' => 'report',
                    'permission' => 'report.revenue-report.daily-summary',
                    'query' => ['tab' => 'daily-summary'],
                ],
                'pending-transaction' => [
                    'title' => 'Pending Transaction',
                    'route' => 'report',
                    'permission' => 'report.revenue-report.pending-transaction',
                    'query' => ['tab' => 'pending-transaction'],
                ],
            ],
        ],
        'bpm' => [
            'type' => 'item',
            'title' => 'BPM',
            'icon' => 'ki-wrench',
            'route' => 'bpm',
            'permission' => 'bpm',
            'items' => [
                'find-task' => [
                    'title' => 'Find Task',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.find-task',
                    'query' => ['tab' => 'find-task'],
                ],
                'instance-query' => [
                    'title' => 'Instance Query',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.instance-query',
                    'query' => ['tab' => 'instance-query'],
                ],
                'process-manager' => [
                    'title' => 'Process Manager',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.process-manager',
                    'query' => ['tab' => 'process-manager'],
                ],
                'worklist-manager' => [
                    'title' => 'Worklist Manager',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.worklist-manager',
                    'query' => ['tab' => 'worklist-manager'],
                ],
                'service-manager' => [
                    'title' => 'Service Manager',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.service-manager',
                    'query' => ['tab' => 'service-manager'],
                ],
                'error-handler' => [
                    'title' => 'Error Handler List',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.error-handler',
                    'query' => ['tab' => 'error-handler'],
                ],
                'component-instance' => [
                    'title' => 'Track Comp. Instance',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.component-instance',
                    'query' => ['tab' => 'component-instance'],
                ],
                'error-handler' => [
                    'title' => 'Error Handler',
                    'route' => 'bpm',
                    'permission' => 'bpm.bpm-api.error-handler',
                    'query' => ['tab' => 'error-handler'],
                ],
            ],
        ],
        'settings' => [
            'type' => 'section',
            'title' => 'App Settings',
            'icon' => 'ki-setting',
            'route' => 'settings',
            'query' => ['tab' => 'users'],
            'permission' => 'settings',
            'hidden' => true,
            'items' => [
                'users' => [
                    'title' => 'Users',
                    'route' => 'settings',
                    'query' => ['tab' => 'users'],
                ],
                'groups' => [
                    'title' => 'Groups',
                    'route' => 'settings',
                    'query' => ['tab' => 'groups'],
                ],
                'roles' => [
                    'title' => 'Roles',
                    'route' => 'settings',
                    'query' => ['tab' => 'roles'],
                ],
                'permissions' => [
                    'title' => 'Permissions',
                    'route' => 'settings',
                    'query' => ['tab' => 'permissions'],
                ],
                'acl' => [
                    'title' => 'Access Control List',
                    'route' => 'settings',
                    'query' => ['tab' => 'access-control-list'],
                ],
            ],
        ],

        // 'prod-support' => [
        //     'type' => 'section',
        //     'title' => 'Prod Support',
        //     'icon' => 'ki-support',
        //     'route' => 'prod-support',
        //     'query' => ['tab' => 'scheduled-patching'],
        //     'permission' => 'prod-support',
        //     'items' => [
        //         'scheduled-patching' => [
        //             'title' => 'Scheduled Patching',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'scheduled-patching'],
        //         ],
        //         'urgent-patching' => [
        //             'title' => 'Urgent Patching',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'urgent-patching'],
        //         ],
        //         'history' => [
        //             'title' => 'History',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'history'],
        //         ],
        //         'report' => [
        //             'title' => 'Report',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'report'],
        //         ],
        //         'data-lookup' => [
        //             'title' => 'Data Lookup',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'data-lookup'],
        //         ],
        //         'approver' => [
        //             'title' => 'Approver',
        //             'route' => 'prod-support',
        //             'query' => ['tab' => 'approver'],
        //         ],
        //     ],
        // ],

    ],
];