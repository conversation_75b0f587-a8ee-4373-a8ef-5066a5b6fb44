<?php

namespace App\Livewire\Module\Supplier\SupplierSearch;

use App\Livewire\Module\Supplier\SupplierSearch;
use App\Traits\EpTrait;
use Auth;

class SupplierSearchCustomerService extends SupplierSearch
{
    use EpTrait;

    public function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.supplier-cs', $action);
    }

    public function searchSuppliers()
    {
        try {
            $collectionData = collect();
            $this->searchValue = $this->supplierSearchForm->search;
            $type = $this->getSearchType($this->searchValue);

            if ($type === 'IcNo') {
                $listSupplier = $this->supplierService->getSMSupplierUsersByIcNo($this->searchValue);
            } else {
                // can be type: MOF, ePNo, SUPPLIER_NAME
                $listSupplier = $this->supplierService->getSMSupplierInfo($this->searchValue, $type);
            }

            if ($listSupplier != null && count($listSupplier) > 0) {
                // Can be more than one company
                foreach ($listSupplier as $supp) {
                    $supplier = $supp;
                    if ($type === 'IcNo') {
                        $supplier = $this->supplierService->getSMSupplierInfo($supp->supplier_id, "ID")->first();
                    }
                    $mofDetail = $this->supplierService->getSMSupplierMofDetail($supp->supplier_id);
                    $listPersonnel = collect($this->supplierService->getSMSupplierUsersActiveBySupplierID($supp->supplier_id));
                    if ($type === 'IcNo') {
                        $listPersonnel = $listPersonnel->where('p_identification_no', $this->searchValue)->all();
                    } else {
                        $listPersonnel = $listPersonnel->whereIn('p_ep_role', ['MOF_SUPPLIER_ADMIN', 'MOF_SUPPLIER_USER', 'BASIC_SUPPLIER_ADMIN', 'BASIC_SUPPLIER_USER', 'FL_USER', 'G2G_ADMIN', 'GOVT_SELLER', 'SUPPLIER_TEMP'])->all();
                    }

                    //Repopulate ListPersonnel
                    foreach ($listPersonnel as $data) {
                        $this->supplierService->populatePersonnelUserData($data);
                    }

                    $applDetail = $this->supplierService->getSmApplDetail($supp->latest_appl_id);
                    $listSuppTrackDiary = null != $applDetail ? $this->supplierService->getTrackingDiarySupplierByDocNo($applDetail->appl_no) : null;
                    $listAttachmentCancelReject = $this->supplierService->getListAttachmentRejectOrCancel($supp->latest_appl_id);
                    $listRemarksCancelReject = $this->supplierService->getListRemarksRejectOrCancel($supp->latest_appl_id);
                    $totalItems = $this->supplierService->getTotalItemsSupplier($supp->supplier_id);
                    $sapVendorCode = $this->supplierService->getMainSapVendorCode($supp->ep_no);
                    $suppMofStatus = $this->supplierService->getSupplierMofStatus($supp->supplier_id);
                    $listinProgressSuppProcessAppl = $this->supplierService->getInProgressWorkFlowSupplierProcess($supp->supplier_id);
                    $listInProgressSuppTrackDiary = null;

                    if ($applDetail && count($listinProgressSuppProcessAppl) > 0) {
                        $listInProgressSuppTrackDiary = $this->supplierService->getTrackingDiarySupplierByDocNo($applDetail->appl_no);
                    }

                    $basicCompInfo = $this->supplierService->getBasicSupplierInfo($supp->latest_appl_id);
                    $listApplRejectReason = $this->supplierService->getListApplRejectReason($supp->latest_appl_id);
                    $listApplSectionReview = $this->supplierService->getListApplSectionReview($supp->latest_appl_id);

                    if ($basicCompInfo && $basicCompInfo != null) {
                        $basicCompInfo->is_with_federal = EpTrait::$YES_NO[$basicCompInfo->is_with_federal];
                        $basicCompInfo->is_with_state = EpTrait::$YES_NO[$basicCompInfo->is_with_state];
                        $basicCompInfo->is_with_statutory = EpTrait::$YES_NO[$basicCompInfo->is_with_statutory];
                        $basicCompInfo->is_with_glc = EpTrait::$YES_NO[$basicCompInfo->is_with_glc];
                        $basicCompInfo->is_with_others = EpTrait::$YES_NO[$basicCompInfo->is_with_others];
                        $basicCompInfo->is_name_hq_non_ascii = false;

                        if (mb_check_encoding($basicCompInfo->company_name, 'ASCII') == false) {
                            $basicCompInfo->is_name_hq_non_ascii = true;
                        }

                        $basicCompInfo->nonAsciiHqDetected = false;
                        if (mb_check_encoding($basicCompInfo->address_1, 'ASCII') == false) {
                            $basicCompInfo->nonAsciiHqDetected = true;
                        }
                        if (mb_check_encoding($basicCompInfo->address_2, 'ASCII') == false) {
                            $basicCompInfo->nonAsciiHqDetected = true;
                        }
                        if (mb_check_encoding($basicCompInfo->address_3, 'ASCII') == false) {
                            $basicCompInfo->nonAsciiHqDetected = true;
                        }
                    }

                    $listApplHistoryDetails = $this->supplierService->getHistoryApplCsView($supp->supplier_id);
                    $listApplHistoryDetails = collect($listApplHistoryDetails)->take(5);

                    foreach ($listApplHistoryDetails as $row) {
                        $listRemarksCancelReject = $this->supplierService->getListRemarksRejectOrCancel($row->appl_id);
                        $row->listRemarksCancelReject = $listRemarksCancelReject;
                        $listAttachmentCancelReject = $this->supplierService->getListAttachmentRejectOrCancel($row->appl_id);
                        $row->listAttachmentCancelReject = $listAttachmentCancelReject;
                        $listApplRejectReason = $this->supplierService->getListApplRejectReason($row->appl_id);
                        $row->listApplRejectReason = $listApplRejectReason;
                        $listApplSectionReview = $this->supplierService->getListApplSectionReview($row->appl_id);
                        $row->listApplSectionReview = $listApplSectionReview;
                    }

                    $dataDetailSupp = [
                        'supplier' => $supplier,
                        'listPersonnel' => $listPersonnel,
                        'applDetail' => $applDetail,
                        'totalItems' => $totalItems,
                        'sapVendorCode' => $sapVendorCode,
                        'basicCompInfo' => $basicCompInfo,
                        'mofDetail' => $mofDetail,
                        'listSuppTrackDiary' => $listSuppTrackDiary,
                        'suppMofStatus' => $suppMofStatus,
                        'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                        'listInProgressSuppTrackDiary' => $listInProgressSuppTrackDiary,
                        'listAttachmentCancelReject' => $listAttachmentCancelReject,
                        'listRemarksCancelReject' => $listRemarksCancelReject,
                        'listApplRejectReason' => $listApplRejectReason,
                        'listApplSectionReview' => $listApplSectionReview,
                        'listApplHistoryDetails' => $listApplHistoryDetails
                    ];

                    $collectionData->push($dataDetailSupp);
                }
            }

            $this->results = $collectionData;
            // dd($this->results);  // Uncomment for debugging

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while searching: ' . $e->getMessage());
            $this->results = [];
        }
    }

    public function render()
    {
        return view('livewire.supplier.supplier-search-customer-service', [
            'suppliers' => $this->results
        ]);
    }
}