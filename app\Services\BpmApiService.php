<?php

namespace App\Services;

use DB;
use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Exception;
use Log;

class BpmApiService {

    public function apiServices ($action, $data) {
        $result = null;
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.63.232:6060/ep-support-middleware");  
            $url = $urlMiddleware;
            $method = "GET";
            $options = [];
            $client = '';

            switch($action) {
                case "task" : 
                    $doc_no = $data->pull("doc_no");
                    $module = $data->pull("module");
                    $created_date = $data->pull("created_date");
                    $url .= "/bpm/task?doc_no=$doc_no&module=$module&created_date=$created_date";
                    break;
                case "listComposites" :
                    $url .= "/bpm/listComposites";
                    break;
                case "error_handler" :
                    $task_title = $data->pull("task_title");
                    $status_id = $data->pull("status_id");
                    $url .= "/bpm/err/list/id?user=cdcadmin&task_title=$task_title&status_id=$status_id&offset=0&limit=50";
                    break;
                case "instance_query":
                    $method = "POST";
                    $options = [
                        'json' => [
                            "user" => null,
                            "token" => null,
                            "compositeDN" => $data->pull("composite"),
                            "createdDtFrom" => $data->pull("date_from"),
                            "createdDtTo" => $data->pull("date_to"),
                            "createdTsFrom" => $data->pull("time_from"),
                            "createdTsTo" => $data->pull("time_to"),
                            "state" => $data->pull("state"),
                            "offset" => 0,
                            "limit" => 50
                        ]
                    ];
                    $url .= "/bpm/instance/query";
                    $client = Http::baseUrl($url);
            }

            try {
                if($method == "GET") {
                    $response = Http::get($url);
                } else {
                    $response = $client->post($url,$options);
                }
                Log::info($response);
                $resultResp = json_decode($response->getBody(), true);
                if($resultResp) {
                    $result = $resultResp;
                    // array(
                    //     "status" => $resultResp["status"],
                    //     "result" => $resultResp["result"] ? $resultResp["result"] : null,
                    //     "message" => $resultResp["message"]? $resultResp["message"] : null );
                }
            } catch (Exception $ex) {
                $result = array(
                    "status" => 'Failed',
                    "result" => $ex->getMessage());
            }
        } catch (Exception $ex) {
            $result = array(
                "status" => 'Failed',
                "result" => $ex->getMessage());
        }
        // Log::info($result);
        return $result;
    }
}