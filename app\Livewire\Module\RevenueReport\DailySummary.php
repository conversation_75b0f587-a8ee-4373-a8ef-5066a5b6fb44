<?php

namespace App\Livewire\Module\RevenueReport;

use App\Livewire\Forms\RevenueReport\DailySummarySearchForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;

class DailySummary extends Component
{
    protected RevenueService $RevenueService;
    public DailySummarySearchForm $dailySummarySearchForm;

    // Public properties to store data
    public $listdata = [];
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public $activeTab = 'gm';

    public function setTab($tab)
    {
        $this->activeTab = $tab;
    }
 
    public function render()
    {
        return view('livewire.revenue-report.daily-summary', []);
    }
}