<?php

namespace App\Livewire;

use App\Livewire\Forms\LoginForm;
use App\Models\EpLoginHistory;
use App\Models\Group;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\Attributes\Layout;

#[Layout('layouts.guest')]
class Login extends Component
{
    public LoginForm $form;

    private $crmCredentialError = false;

    private const ERROR_INVALID_CREDENTIALS = 'Invalid username or password. Please check your credentials and try again.';

    public function login()
    {
        $this->form->validate();

        $localUser = User::where('user_name', $this->form->user_name)->first();

        if ($localUser) {
            // User exists in local database
            if ($localUser->is_crm && $localUser->is_ldap) {
                // If both flags are true, try CRM first
                if (config('login.enable_crm')) {
                    $crmLoginResult = $this->attemptCrmLogin();
                    if ($crmLoginResult !== false) {
                        return $crmLoginResult;
                    }
                    // Only check credentials failure here
                    if ($this->hasCrmCredentialError()) {
                        $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
                        return;
                    }
                }

                // If CRM login fails or is disabled, try LDAP
                if (config('login.enable_ldap')) {
                    return $this->attemptLdapLogin();
                }

                // If both are disabled, fallback to EPSS
                return $this->attemptEpssLogin($localUser);
            }

            if ($localUser->is_ldap) {
                if (config('login.enable_ldap')) {
                    return $this->attemptLdapLogin();
                }
                // Fallback to EPSS if LDAP is disabled
                return $this->attemptEpssLogin($localUser);
            }

            if ($localUser->is_crm) {
                if (config('login.enable_crm')) {
                    $crmLoginResult = $this->attemptCrmLogin();
                    if ($crmLoginResult !== false) {
                        return $crmLoginResult;
                    }
                    if ($this->hasCrmCredentialError()) {
                        $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
                        return;
                    }
                }
                // Fallback to EPSS if CRM is disabled
                return $this->attemptEpssLogin($localUser);
            }

            // If no special flags, use EPSS
            return $this->attemptEpssLogin($localUser);
        } else {
            // User not found in local database - try CRM first which can create new users
            if (config('login.enable_crm')) {
                return $this->attemptCrmLogin();
            }

            $this->addError('form.user_name', 'LDAP Users requires pre-existing account. User not found. Please contact your administrator.');
            return;
        }
    }

    protected function hasCrmCredentialError()
    {
        return $this->crmCredentialError;
    }

    protected function attemptLdapLogin()
    {
        try {
            $ldapCredentials = [
                'samaccountname' => $this->form->user_name,
                'password' => $this->form->password,
            ];

            $ldapSuccess = Auth::guard('ldap')->attempt($ldapCredentials, $this->form->remember);

            if ($ldapSuccess) {
                $localUser = User::where('user_name', $this->form->user_name)->first();

                if (!$localUser) {
                    $this->addError('form.user_name', 'Your LDAP account is not linked to a local account. Please contact your administrator.');
                    Auth::guard('ldap')->logout();
                    return false;
                }

                if (!$localUser->is_active) {
                    $this->addError('form.user_name', 'Your account is inactive. Please contact your administrator.');
                    Auth::guard('ldap')->logout();
                    return false;
                }

                // Logout from LDAP guard and login with web guard using the local user
                Auth::guard('ldap')->logout();
                Auth::guard('web')->login($localUser, $this->form->remember);

                session()->regenerate();

                $this->recordLoginHistory($localUser);

                return redirect()->intended('/home');
            }

            $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
            return false;
        } catch (\Exception $e) {
            Log::error('LDAP authentication error: ' . $e->getMessage());
            $this->addError('form.user_name', 'LDAP authentication error: ' . $e->getMessage());
            return false;
        }
    }

    protected function attemptEpssLogin($user)
    {
        if (Hash::check($this->form->password, $user->password)) {
            Auth::guard('web')->login($user, $this->form->remember);
            session()->regenerate();

            $this->recordLoginHistory($user);

            return redirect()->intended('/home');
        }

        $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
        return false;
    }

    protected function attemptCrmLogin()
    {
        try {
            $crmUser = DB::connection('cdccrm')
                ->table('users')
                ->select('users.*', 'email_addresses.email_address')
                ->leftJoin('email_addr_bean_rel', function ($join) {
                    $join->on('users.id', '=', 'email_addr_bean_rel.bean_id')
                        ->where('email_addr_bean_rel.bean_module', '=', 'Users')
                        ->where('email_addr_bean_rel.deleted', '=', 0)
                        ->where('email_addr_bean_rel.primary_address', '=', 1);
                })
                ->leftJoin('email_addresses', function ($join) {
                    $join->on('email_addr_bean_rel.email_address_id', '=', 'email_addresses.id')
                        ->where('email_addresses.deleted', '=', 0);
                })
                ->where('users.user_name', $this->form->user_name)
                ->where('users.deleted', 0)
                ->first();

            if (!$crmUser) {
                $this->crmCredentialError = true;
                $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
                return false;
            }

            if ($crmUser->status !== 'Active') {
                $this->addError('form.user_name', 'Your CRM account is inactive. Please contact your administrator.');
                return false;
            }

            // Verify password using the same logic as CRM
            $password_md5 = md5($this->form->password);
            $user_hash = $crmUser->user_hash;
            $passwordValid = false;

            if ($user_hash[0] != '$' && strlen($user_hash) == 32) {
                // Old way - just md5 password
                if (strtolower(md5($this->form->password)) == $user_hash) {
                    $passwordValid = true;
                }
            }

            if (crypt(strtolower($password_md5), $user_hash) == $user_hash) {
                $passwordValid = true;
            }

            if (!$passwordValid) {
                $this->crmCredentialError = true;
                $this->addError('form.user_name', self::ERROR_INVALID_CREDENTIALS);
                return false;
            }

            $securityGroups = DB::connection('cdccrm')
                ->table('securitygroups_users')
                ->select('securitygroups.name')
                ->join('securitygroups', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id')
                ->where('securitygroups_users.user_id', $crmUser->id)
                ->where('securitygroups_users.deleted', 0)
                ->where('securitygroups.deleted', 0)
                ->pluck('name')
                ->toArray();

            $groupIds = [];
            foreach ($securityGroups as $groupName) {
                $group = Group::firstOrCreate(
                    ['name' => $groupName],
                    ['description' => $groupName . ' Group']
                );
                $groupIds[] = $group->id;
            }

            $userData = [
                'user_name' => $crmUser->user_name,
                'first_name' => $crmUser->first_name,
                'last_name' => $crmUser->last_name,
                'name' => $crmUser->first_name . ' ' . $crmUser->last_name,
                'email' => $crmUser->email_address ?? $crmUser->user_name . '@commercedc.com.my',
                'password' => Hash::make($this->form->password),
                'is_crm' => true,
                'is_ldap' => false,
                'is_active' => ($crmUser->status === 'Active'),
            ];

            $user = User::updateOrCreate(
                ['user_name' => $crmUser->user_name],
                $userData
            );

            $user->groups()->sync($groupIds);

            $this->recordLoginHistory($user, $securityGroups);

            Auth::guard('web')->login($user, $this->form->remember);
            session()->regenerate();

            return redirect()->intended('/home');
        } catch (\Exception $e) {
            Log::error('CRM authentication error: ' . $e->getMessage());
            return false;
        }
    }

    protected function recordLoginHistory($user, $securityGroups = null)
    {
        if ($securityGroups === null) {
            $securityGroups = isset($user->groups) ? $user->groups->pluck('name')->toArray() : [];
        }

        EpLoginHistory::create([
            'user_id' => $user->id,
            'username' => $user->user_name,
            'fullname' => $user->name,
            'email' => $user->email,
            'groups' => $securityGroups,
            'last_login' => now()
        ]);
    }

    protected function getEnabledAuthMethods()
    {
        $methods = [];

        if (config('login.enable_ldap')) {
            $methods[] = 'LDAP';
        }

        if (config('login.enable_crm')) {
            $methods[] = 'CRM';
        }

        // EPSS is always available as fallback
        $methods[] = 'EPSS';

        return $methods;
    }

    public function render()
    {
        return view('authentication.login', [
            'enabledAuthMethods' => $this->getEnabledAuthMethods()
        ]);
    }
}