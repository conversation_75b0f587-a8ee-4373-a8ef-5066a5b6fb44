<?php

namespace App\Livewire\Module;

use Livewire\Component;

class ProdSupport extends Component
{
    public $activeTab;
    
    public function mount()
    {
        // Get active tab from query string, default to scheduled-patching
        $this->activeTab = request()->query('tab', 'scheduled-patching');
    }
    
    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        return redirect()->route('prod-support', ['tab' => $tab]);
    }
    
    public function render()
    {
        return view('livewire.prod-support.index');
    }
}