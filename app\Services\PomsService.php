<?php

namespace App\Services;

use DB;
use Carbon\Carbon;
use Log;
use GuzzleHttp\Client;

class PomsService
{
    public function getTalkdeskData()
    {
        $results = DB::connection('mysql_poms')
            ->table('sla_mitel')
            ->orderBy('date_call', 'desc')
            ->get();
        return $results;
    }

    public function getTalkdeskMonthlyData()
    {
        $results = DB::connection('mysql_poms')
            ->table('sla_talkdesk_monthly')
            ->orderBy('date_call', 'desc')
            ->get();
        return $results;
    }
}