<?php

namespace App\Livewire\Forms\AppSetting;

use Illuminate\Validation\Rule;
use Livewire\Form;
use App\Models\Permission;

class PermissionForm extends Form
{
    public $permissionId;
    public $permissionName = '';
    public $permissionAction = '';
    public $permissionRouteName = '';
    public $permissionDescription = '';

    public function rules()
    {
        return [
            'permissionName' => 'required',
            'permissionAction' => [
                'required',
                Rule::unique('permissions', 'action')
                    ->where('route_name', $this->permissionRouteName)
                    ->ignore($this->permissionId)
            ],
            'permissionRouteName' => [
                'required',
                Rule::unique('permissions', 'route_name')
                    ->where('action', $this->permissionAction)
                    ->ignore($this->permissionId)
            ],
        ];
    }

    public function messages()
    {
        return [
            'permissionAction.unique' => 'The combination of action and route name must be unique.',
            'permissionRouteName.unique' => 'The combination of route name and action must be unique.'
        ];
    }

    public function save()
    {
        $permissionData = [
            'name' => $this->permissionName,
            'action' => $this->permissionAction,
            'route_name' => $this->permissionRouteName,
            'description' => $this->permissionDescription,
        ];

        if ($this->permissionId) {
            $permission = Permission::find($this->permissionId);
            $permission->update($permissionData);
        } else {
            $permission = Permission::create($permissionData);
        }

        return $permission;
    }

    public function edit(Permission $permission)
    {
        $this->permissionId = $permission->id;
        $this->permissionName = $permission->name;
        $this->permissionAction = $permission->action;
        $this->permissionRouteName = $permission->route_name;
        $this->permissionDescription = $permission->description;
    }

    public function resetForm()
    {
        $this->permissionId = null;
        $this->permissionName = '';
        $this->permissionAction = '';
        $this->permissionRouteName = '';
        $this->permissionDescription = '';
    }
}