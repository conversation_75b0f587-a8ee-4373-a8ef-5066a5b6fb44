<?php

namespace App\Livewire\Component;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class Header extends Component
{
    public $user;
    public $breadcrumbs;

    public function mount()
    {
        $authUser = auth()->user();
        if ($authUser) {
            $this->user = [
                'first_name' => $authUser->first_name,
                'last_name' => $authUser->last_name,
                'name' => $authUser->name,
                'user_name' => $authUser->user_name,
                'email' => $authUser->email,
            ];
        }

        $this->processBreadcrumbs();
    }

    private function processBreadcrumbs()
    {
        $menuConfig = config('menu.items');
        $currentRoute = request()->route()->getName();
        $currentTab = request()->query('tab');

        $this->breadcrumbs = [];

        // Find the matching menu item and build breadcrumbs
        $matchedItem = $this->findMenuItemByRoute($menuConfig, $currentRoute, $currentTab);

        if ($matchedItem) {
            $this->breadcrumbs = $this->buildBreadcrumbsFromMenu($matchedItem);
        } else {
            // Fallback to the original logic if no menu match found
            $this->buildBreadcrumbsFromPath();
        }
    }

    private function findMenuItemByRoute($menuItems, $route, $tab = null, $breadcrumbPath = [])
    {
        foreach ($menuItems as $key => $item) {
            $currentPath = array_merge($breadcrumbPath, [$item]);

            // Check if this item matches the current route
            if (isset($item['route']) && $item['route'] === $route) {
                // If no tab is specified, or if tab matches
                if (!$tab || (isset($item['query']['tab']) && $item['query']['tab'] === $tab)) {
                    return $currentPath;
                }
            }

            // Search in sub-items
            if (isset($item['items']) && is_array($item['items'])) {
                $found = $this->findMenuItemByRoute($item['items'], $route, $tab, $currentPath);
                if ($found) {
                    return $found;
                }
            }
        }

        return null;
    }

    private function buildBreadcrumbsFromMenu($menuPath)
    {
        $breadcrumbs = [];
        $totalItems = count($menuPath);

        foreach ($menuPath as $index => $item) {
            $isLast = ($index === $totalItems - 1);

            $breadcrumbs[] = [
                'title' => $item['title'],
                'is_last' => $isLast,
                'route' => $item['route'] ?? null,
                'query' => $item['query'] ?? null,
            ];
        }

        return $breadcrumbs;
    }

    private function buildBreadcrumbsFromPath()
    {
        $path = request()->path();
        $segments = $path === '/' ? [] : explode('/', $path);

        foreach ($segments as $index => $segment) {
            $segmentTitle = ucfirst(str_replace('-', ' ', $segment));
            $isLast = ($index === count($segments) - 1) && !request()->has('tab');

            $this->breadcrumbs[] = [
                'title' => $segmentTitle,
                'is_last' => $isLast,
                'route' => null,
                'query' => null,
            ];
        }

        // Add tab as a breadcrumb if it exists
        if (request()->has('tab')) {
            $tabName = request()->query('tab');
            $tabTitle = ucfirst(str_replace('-', ' ', $tabName));

            $this->breadcrumbs[] = [
                'title' => $tabTitle,
                'is_last' => true,
                'route' => null,
                'query' => null,
            ];
        }
    }

    public function logout()
    {
        // Check if the user is authenticated through the web guard
        if (Auth::guard('web')->check()) {
            Auth::guard('web')->logout();
        }

        // Check if the user is authenticated through the ldap guard
        if (Auth::guard('ldap')->check()) {
            Auth::guard('ldap')->logout();
        }

        session()->invalidate();
        session()->regenerateToken();

        return redirect()->route('login');
    }

    public function render()
    {
        return view('livewire.component.header', [
            'user' => $this->user,
            'breadcrumbs' => $this->breadcrumbs,
        ]);
    }
}