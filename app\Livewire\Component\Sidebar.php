<?php

namespace App\Livewire\Component;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use App\Services\MenuService;

class Sidebar extends Component
{
    public $user;
    public $menuItems = [];
    public $currentRoute;
    public $activeTopLevelItem = 'home'; // Default active top level

    public function mount(MenuService $menuService)
    {
        $this->currentRoute = Request::url();

        $authUser = Auth::user();
        if ($authUser) {
            $this->user = [
                'first_name' => $authUser->first_name,
                'last_name' => $authUser->last_name,
                'name' => $authUser->name,
                'user_name' => $authUser->user_name,
                'email' => $authUser->email,
            ];

            // Use the menu service to build the menu
            $this->menuItems = $menuService->getMenu();
            $this->determineActiveTopLevel();
        }
    }

    // Determine which top-level menu item is active
    protected function determineActiveTopLevel()
    {
        foreach ($this->menuItems as $item) {
            if (($item['active'] ?? false) && isset($item['id'])) {
                $this->activeTopLevelItem = $item['id'];
                break;
            }
        }
    }

    // Get only the menu items related to the active top-level item
    public function getSecondaryMenuItems()
    {
        $filteredItems = [];

        foreach ($this->menuItems as $item) {
            if (($item['id'] ?? '') === $this->activeTopLevelItem) {
                $filteredItems[] = $item;
            }
        }

        return $filteredItems;
    }

    public function logout()
    {
        // Check if the user is authenticated through the web guard
        if (Auth::guard('web')->check()) {
            Auth::guard('web')->logout();
        }

        // Check if the user is authenticated through the ldap guard
        if (Auth::guard('ldap')->check()) {
            Auth::guard('ldap')->logout();
        }

        session()->invalidate();
        session()->regenerateToken();

        return redirect()->route('login');
    }

    public function render()
    {
        return view('livewire.component.sidebar', [
            'menuItems' => $this->menuItems,
            'secondaryMenuItems' => $this->getSecondaryMenuItems()
        ]);
    }
}