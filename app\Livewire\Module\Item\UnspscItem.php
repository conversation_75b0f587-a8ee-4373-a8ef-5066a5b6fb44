<?php

namespace App\Livewire\Module\Item;

use App\Livewire\Forms\Item\UnspscItemSearchForm;
use App\Services\ItemService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Log;

class UnspscItem extends Component
{
    protected ItemService $ItemService;

    public UnspscItemSearchForm $unspscItemSearchForm;

    // Public properties to store data
    public $listdata = []; 
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    // Properties for modal
    public $isModalOpen = false;
    public $modalTitle = '';
    public $modalType = '';
    public $modalFind = '';
    public $listItemType = [];
    public $listItemBrand = [];
    public $listItemMeasurement = [];
    public $modalData = [];
    public $isLoading = false;

    public function boot(ItemService $ItemService)
    {
        $this->itemService = $ItemService;
    }

    public function mount()
    {
        $this->carian = 'Senarai data dari 1 Jan 2018 hingga sekarang';
        $this->unspscItemSearchForm->carian = $this->carian;
        $this->search();
    }

    public function rendered()
    {
        $this->dispatch('refresh-unspsc-item-table');
    } 

    public function search()
    {
        $this->unspscItemSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->unspscItemSearchForm->carian;
        $this->errorMessage = null;

        try {
            $list = array();
            $carian = $this->carian;             
            if(strlen($carian) == 8 && ctype_digit($carian) == true ){
                $list = $this->itemService->getUNSPSCItemDetail("UNSPSC_CODE", $carian); 
            } else if($carian === 'Senarai data dari 1 Jan 2018 hingga sekarang'){
                $list = $this->itemService->getUNSPSCItemDetail("DEFAULT", '2017-12-31'); 
            } else {
                $list = $this->itemService->getUNSPSCItemDetail("UNSPSC_TITLE", '%'.trim(strtoupper($carian)).'%');
            }
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 
 
    public function openUnspscModal($data, $title, $itemDetail = 'type')
    {
        $this->modalFind = $data;
        $this->modalTitle = $title;
        $this->modalType = $itemDetail;
        $this->isLoading = true;
        $this->isModalOpen = true;

        try {
            switch ($itemDetail) {
                case 'type':
                    $this->listItemType = $this->itemService->getListItemTypeByUNSPSCID($data) ?? [];
                    break;

                case 'brand':
                    $this->listItemBrand = $this->itemService->getListItemBrandByUNSPSCID($data) ?? [];
                    break;

                case 'measurement':
                    $this->listItemMeasurement = $this->itemService->getListItemMeasurementByUNSPSCID($data) ?? [];
                    break;

                default:
                    throw new \Exception("Unknown modal type: {$itemDetail}");
            }
        } catch (\Exception $e) {
            $this->modalData = [
                'data' => $data,
                'status' => 'Error',
                'details' => 'An error occurred while fetching data: ' . $e->getMessage()
            ];
            $this->listItemType = [];
            $this->listItemBrand = [];
            $this->listItemMeasurement = [];
        } finally {
            $this->isLoading = false;
        }

        // Dispatch event to initialize the modal
        $this->dispatch('open-document-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->modalFind = '';
    }

    public function render()
    {
        return view('livewire.item.unspsc-item', [
            'listdata' => $this->listdata
        ]);
    }
}