<?php

namespace App\Livewire\Module\Crm;

use App\Services\CRMService;
use Auth;
use DateTime;
use Livewire\Component;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;


class CrmCsCases extends Component
{
    protected CRMService $crmService;
    public $topEnqSubCategories = [];
    public $topIncServSubCategories = [];
    public $dashboardCSData = [];
    public $pendingResponseData = [];
    public $emailInboundData = [];

    // For Modal
    public $selectedType;
    public $modalData;
    public $modalTitle;
    public $isModalOpen = false;
    public $isLoading = false;
    public $modalType = '';

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('crm.cs-cases', $action);
    }

    public function boot(CRMService $crmService)
    {
        $this->crmService = $crmService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $this->searchCsCases();
    }

    public function searchCsCases()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $this->topEnqSubCategories = $this->getDashboardTopEnqSubCategory();
        $this->topIncServSubCategories = $this->getDashboardTopIncServSubCategory();
        $this->dashboardCSData = $this->getDashboardCRMCS();
        $this->pendingResponseData = $this->getDashboardPendingResponse();
        $this->emailInboundData = $this->getCrmSchedulerEmailInbound();

        $this->dispatch('refresh-cs-tables');
    }

    public function downloadTopEnq($subCat, $subCat2)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'Top10_Enquiry_' . $dateNow;
        $list = $this->crmService->getDetailsTopEnqSubCategory($subCat, $subCat2);

        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $spreadsheet->getProperties()
            ->setTitle('List of Top 10 Enquiry');

        // Get active sheet
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('TopEnquiry');

        // Set orientation
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set default style
        $spreadsheet->getDefaultStyle()->getFont()
            ->setName('Calibri')
            ->setSize(11);

        // Set column formats
        $sheet->getStyle('A:F')
            ->getNumberFormat()
            ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);

        // Set headers
        $sheet->setCellValue('A1', 'CRM CASE NO')
            ->setCellValue('B1', 'DATE CREATED')
            ->setCellValue('C1', 'CONTACT MODE')
            ->setCellValue('D1', 'REQUEST TYPE')
            ->setCellValue('E1', 'SUB CATEGORY')
            ->setCellValue('F1', 'SUB CATEGORY 2');

        // Style header row
        $sheet->getStyle('A1:F1')->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => '684E49'],
            ],
            'font' => [
                'color' => ['rgb' => 'FFFFFF'],
                'bold' => true,
                'size' => 11,
                'name' => 'Calibri',
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ]);

        // Auto size columns
        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Add data rows
        $row = 2;
        foreach ($list as $obj) {
            $dateCaseCreated = Carbon::parse($obj->caseCreated)->addHours(8)->format("Y-m-d H:i");

            $sheet->setCellValue('A' . $row, $obj->caseNum)
                ->setCellValue('B' . $row, $dateCaseCreated)
                ->setCellValue('C' . $row, $obj->contactMode)
                ->setCellValue('D' . $row, $obj->requestType)
                ->setCellValue('E' . $row, $obj->subCategory)
                ->setCellValue('F' . $row, $obj->subCategory2);

            $row++;
        }

        // Create directory if it doesn't exist
        if (!file_exists(storage_path('app/Report'))) {
            mkdir(storage_path('app/Report'), 0755, true);
        }

        // Save file
        $filePath = storage_path('app/Report/' . $fileName . '.xlsx');
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        // Return file for download
        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '.xlsx"',
            'Cache-Control' => 'max-age=0',
        ];

        return response()->download($filePath, $fileName . '.xlsx', $headers);
    }

    public function downloadTopIncServ($subCat, $subCat2)
    {
        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'Top10_Incident_Service_' . $dateNow;
        $list = $this->crmService->getDetailsTopIncServSubCategory($subCat, $subCat2);

        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $spreadsheet->getProperties()
            ->setTitle('List of Top 10 Incident Service');

        // Get active sheet
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('TopIncServ');

        // Set orientation
        $sheet->getPageSetup()->setOrientation(\PhpOffice\PhpSpreadsheet\Worksheet\PageSetup::ORIENTATION_LANDSCAPE);

        // Set default style
        $spreadsheet->getDefaultStyle()->getFont()
            ->setName('Calibri')
            ->setSize(11);

        // Set column formats
        $sheet->getStyle('A:F')
            ->getNumberFormat()
            ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);

        // Set headers
        $sheet->setCellValue('A1', 'CRM CASE NO')
            ->setCellValue('B1', 'DATE CREATED')
            ->setCellValue('C1', 'CONTACT MODE')
            ->setCellValue('D1', 'REQUEST TYPE')
            ->setCellValue('E1', 'SUB CATEGORY')
            ->setCellValue('F1', 'SUB CATEGORY 2');

        // Style header row
        $sheet->getStyle('A1:F1')->applyFromArray([
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'color' => ['rgb' => '684E49'],
            ],
            'font' => [
                'color' => ['rgb' => 'FFFFFF'],
                'bold' => true,
                'size' => 11,
                'name' => 'Calibri',
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                ],
            ],
        ]);

        // Auto size columns
        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Add data rows
        $row = 2;
        foreach ($list as $obj) {
            $dateCaseCreated = Carbon::parse($obj->caseCreated)->addHours(8)->format("Y-m-d H:i");

            $sheet->setCellValue('A' . $row, $obj->caseNum)
                ->setCellValue('B' . $row, $dateCaseCreated)
                ->setCellValue('C' . $row, $obj->contactMode)
                ->setCellValue('D' . $row, $obj->requestType)
                ->setCellValue('E' . $row, $obj->subCategory)
                ->setCellValue('F' . $row, $obj->subCategory2);

            $row++;
        }

        // Create directory if it doesn't exist
        if (!file_exists(storage_path('app/Report'))) {
            mkdir(storage_path('app/Report'), 0755, true);
        }

        // Save file
        $filePath = storage_path('app/Report/' . $fileName . '.xlsx');
        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        // Return file for download
        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '.xlsx"',
            'Cache-Control' => 'max-age=0',
        ];

        return response()->download($filePath, $fileName . '.xlsx', $headers);
    }

    public function loadPendingResponseData($type)
    {
        $this->selectedType = $type;
        $this->modalType = 'pending-response';
        $this->isLoading = true;
        $this->isModalOpen = true;

        if ($type == 'openportal') {
            $this->modalTitle = 'List of Cases Pending Response - Open Portal';
        } else {
            $this->modalTitle = 'List of Cases Pending Response - Email';
        }

        try {
            $CASE_CONTACT_MODE = ($type == 'openportal') ? 'Open Portal' : 'Email';
            $list = $this->crmService->getDashboardPendingResponse();
            $formattedData = [];
            $counter = 0;

            foreach ($list as $value) {
                if ($value->contact_mode == $CASE_CONTACT_MODE) {
                    $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                    $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
                    $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

                    // Format case status
                    $subStatus = $value->case_status;
                    $caseStatusMap = [
                        'Open_Pending Input' => 'Pending Input',
                        'Closed_Closed' => 'Closed',
                        'Open_Assigned' => 'Assigned',
                        'Pending_User_Verification' => 'Pending User Verification',
                        'Closed_Rejected' => 'Rejected',
                        'Open_New' => 'New',
                        'Closed_Duplicate' => 'Duplicate',
                        'Open_Pending_Approval' => 'Pending Approval',
                        'Closed_Approved' => 'Approved',
                        'In_Progress' => 'In Progress',
                        'Open_Resolved' => 'Resolved'
                    ];

                    $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                    // Calculate time remaining
                    $current = Carbon::now();
                    $actualSLAEnd = $value->cs_completed_datetime;
                    $slaEnd = Carbon::parse($actualSLAEnd)->format("Y-m-d H:i:s");
                    $dateDiff = $current->diff(new DateTime($slaEnd));
                    $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                    $formattedData[] = [
                        'counter' => ++$counter,
                        'case_id' => $value->case_id,
                        'case_number' => $value->case_number,
                        'case_state' => $value->case_state,
                        'case_status' => $caseStatus,
                        'sla_start' => $slaStart,
                        'sla_end' => $slaStop,
                        'scheduler_action' => $slaStopbyScheduler,
                        'time_remaining' => $timeRemaining
                    ];
                }
            }

            $this->modalData = $formattedData;
        } catch (\Exception $e) {
            $this->modalData = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-cs-details-modal');
    }

    public function loadCaseData($contactMode, $caseType)
    {
        $this->selectedType = $caseType;
        $this->modalType = 'total-cases';
        $this->isLoading = true;
        $this->isModalOpen = true;

        // Set the modal title based on contact mode and case type
        $titles = [
            'responseexceedsla' => 'List of Cases Response > 15 Minute',
            'noresponseexceedsla' => 'List of Cases Pending Response > 15 Minute',
            'responsewithinsla' => 'List of Cases Response within 15 Minute',
            'noresponsewithinsla' => 'List of Cases Pending Response within 15 Minute',
            'noresponsewithinslabyScheduler' => 'List of Pending Response Cases Taken by Scheduler'
        ];

        $contactModeLabel = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $this->modalTitle = $titles[$caseType] . ' - ' . $contactModeLabel;

        try {
            // Process data based on contact mode and case type
            switch ($caseType) {
                case 'responseexceedsla':
                    $this->modalData = $this->formatResponseExceedSLAData($contactMode);
                    break;
                case 'noresponseexceedsla':
                    $this->modalData = $this->formatNoResponseExceedSLAData($contactMode);
                    break;
                case 'responsewithinsla':
                    $this->modalData = $this->formatResponseWithinSLAData($contactMode);
                    break;
                case 'noresponsewithinsla':
                    $this->modalData = $this->formatNoResponseWithinSLAData($contactMode);
                    break;
                case 'noresponsewithinslabyScheduler':
                    $this->modalData = $this->formatNoResponseWithinSLABySchedulerData($contactMode);
                    break;
                default:
                    $this->modalData = [];
            }
        } catch (\Exception $e) {
            $this->modalData = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-cs-details-modal');
    }

    // Helper methods to format different types of data
    private function formatResponseExceedSLAData($contactMode)
    {
        $CASE_CONTACT_MODE = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $list = $this->crmService->getDashboardCS();
        $formattedData = [];
        $counter = 0;

        foreach ($list as $value) {
            if ($value->contact_mode == $CASE_CONTACT_MODE && $value->cs_completed_datetime != null) {
                $availableSLAEnd = $value->cs_due_datetime;
                $actualSLAEnd = $value->cs_completed_datetime;

                if ($actualSLAEnd > $availableSLAEnd) {
                    $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                    $slaStop = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

                    // Format request type and incident type
                    $requestType = '';
                    $typeOfIncident = '';

                    if ($contactMode == 'openportal') {
                        $incidentType = $value->type_of_incident;
                        if ($incidentType == 'service_business') {
                            $typeOfIncident = 'Business Service';
                            $requestType = 'Service';
                        } elseif ($incidentType == 'service_it') {
                            $typeOfIncident = 'IT Service';
                            $requestType = 'Service';
                        } elseif ($incidentType == 'incident_business') {
                            $typeOfIncident = 'Business Incident';
                            $requestType = 'Incident';
                        } elseif ($incidentType == 'incident_it') {
                            $typeOfIncident = 'IT Incident';
                            $requestType = 'Incident';
                        } elseif ($incidentType == '') {
                            $typeOfIncident = '';
                            $requestType = 'Enquiry';
                        }

                        $respondBy = ($value->case_status == 'Closed_Cancelled_Eaduan' &&
                            ($value->request_type == null || $value->request_type == ''))
                            ? 'eAduan User' : $value->csName;
                    } else {
                        // Email specific logic
                        $requestType = $value->request_type;
                        if ($requestType == 'enquiry') {
                            $typeOfIncident = '';
                            $requestType = 'enquiry';
                        } elseif ($requestType == 'service') {
                            if ($value->type_of_incident == 'service_it') {
                                $typeOfIncident = 'IT Service';
                                $requestType = 'Service';
                            } elseif ($value->type_of_incident == 'service_business') {
                                $typeOfIncident = 'Business Service';
                                $requestType = 'Service';
                            }
                        } elseif ($requestType == 'incident') {
                            if ($value->type_of_incident == 'incident_it') {
                                $typeOfIncident = 'IT Incident';
                                $requestType = 'Incident';
                            } elseif ($value->type_of_incident == 'incident_business') {
                                $typeOfIncident = 'Business Incident';
                                $requestType = 'Incident';
                            }
                        } elseif ($requestType == '' || $requestType == null) {
                            $typeOfIncident = '';
                            $requestType = '';
                        }

                        $respondBy = $value->csName ?? $value->csNama;
                    }

                    // Format case status
                    $subStatus = $value->case_status;
                    $caseStatusMap = [
                        'Open_Pending Input' => 'Pending Input',
                        'Closed_Closed' => 'Closed',
                        'Open_Assigned' => 'Assigned',
                        'Closed_Cancelled_Eaduan' => 'Cancelled by eAduan',
                        'Closed_Verified_Eaduan' => 'Closed by eAduan',
                        'Closed_Rejected_Eaduan' => 'Rejected by eAduan',
                        'Pending_User_Verification' => 'Pending User Verification',
                        'Closed_Rejected' => 'Rejected',
                        'Open_New' => 'New',
                        'Closed_Duplicate' => 'Duplicate',
                        'Open_Pending_Approval' => 'Pending Approval',
                        'Closed_Approved' => 'Approved',
                        'In_Progress' => 'In Progress',
                        'Open_Resolved' => 'Resolved'
                    ];
                    $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                    // Format case info
                    $infoC = $value->case_info;
                    $caseInfoMap = [
                        'info_completed' => 'Complete',
                        'info_notcompleted' => 'Pending More Info',
                        'rejected' => 'Rejected',
                        '' => ''
                    ];
                    $caseInfoC = $caseInfoMap[$infoC] ?? $infoC;

                    $formattedData[] = [
                        'counter' => ++$counter,
                        'case_id' => $value->case_id,
                        'case_number' => $value->case_number,
                        'case_state' => $value->case_state,
                        'case_status' => $caseStatus,
                        'case_info' => $caseInfoC,
                        'request_type' => $requestType,
                        'incident_type' => $typeOfIncident,
                        'sla_start' => $slaStart,
                        'sla_end' => $slaStop,
                        'respond_by' => $respondBy
                    ];
                }
            }
        }

        return $formattedData;
    }

    private function formatNoResponseExceedSLAData($contactMode)
    {
        $CASE_CONTACT_MODE = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $list = $this->crmService->getDashboardCS();
        $formattedData = [];
        $counter = 0;
        $current = Carbon::now();

        foreach ($list as $value) {
            if ($value->contact_mode == $CASE_CONTACT_MODE && $value->cs_completed_datetime == null) {
                $availableSLAEnd = $value->cs_due_datetime;

                if ($availableSLAEnd < $current) {
                    $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                    $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

                    // Format case status
                    $subStatus = $value->case_status;
                    $caseStatusMap = [
                        'Open_Pending Input' => 'Pending Input',
                        'Closed_Closed' => 'Closed',
                        'Open_Assigned' => 'Assigned',
                        'Closed_Cancelled_Eaduan' => 'Cancelled by eAduan',
                        'Closed_Verified_Eaduan' => 'Closed by eAduan',
                        'Closed_Rejected_Eaduan' => 'Rejected by eAduan',
                        'Pending_User_Verification' => 'Pending User Verification',
                        'Closed_Rejected' => 'Rejected',
                        'Open_New' => 'New',
                        'Closed_Duplicate' => 'Duplicate',
                        'Open_Pending_Approval' => 'Pending Approval',
                        'Closed_Approved' => 'Approved',
                        'In_Progress' => 'In Progress',
                        'Open_Resolved' => 'Resolved'
                    ];
                    $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                    // Calculate time exceeded
                    $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
                    $dateDiff = $current->diff(new DateTime($slaEnd));
                    $timeRemaining = '> ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                    $formattedData[] = [
                        'counter' => ++$counter,
                        'case_id' => $value->case_id,
                        'case_number' => $value->case_number,
                        'case_state' => $value->case_state,
                        'case_status' => $caseStatus,
                        'sla_start' => $slaStart,
                        'sla_end' => $slaStop,
                        'time_exceeded' => $timeRemaining
                    ];
                }
            }
        }

        return $formattedData;
    }

    private function formatResponseWithinSLAData($contactMode)
    {
        $CASE_CONTACT_MODE = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $list = $this->crmService->getDashboardCS();
        $formattedData = [];
        $counter = 0;

        foreach ($list as $value) {
            if ($value->contact_mode == $CASE_CONTACT_MODE && $value->cs_completed_datetime != null) {
                $availableSLAEnd = $value->cs_due_datetime;
                $actualSLAEnd = $value->cs_completed_datetime;

                if ($actualSLAEnd <= $availableSLAEnd) {
                    // Check additional conditions
                    $shouldInclude = false;
                    if ($contactMode == 'openportal') {
                        $shouldInclude = ($value->request_type != '' || $value->case_info != '') ||
                            ($value->case_status == 'Closed_Cancelled_Eaduan');
                    } else {
                        $shouldInclude = ($value->request_type != '' || $value->case_info != '');
                    }

                    if ($shouldInclude) {
                        $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                        $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

                        // Format request type and incident type
                        $requestType = '';
                        $typeOfIncident = '';

                        if ($contactMode == 'openportal') {
                            $incidentType = $value->type_of_incident;
                            if ($incidentType == 'service_business') {
                                $typeOfIncident = 'Business Service';
                                $requestType = 'Service';
                            } elseif ($incidentType == 'service_it') {
                                $typeOfIncident = 'IT Service';
                                $requestType = 'Service';
                            } elseif ($incidentType == 'incident_business') {
                                $typeOfIncident = 'Business Incident';
                                $requestType = 'Incident';
                            } elseif ($incidentType == 'incident_it') {
                                $typeOfIncident = 'IT Incident';
                                $requestType = 'Incident';
                            } elseif ($incidentType == '') {
                                $typeOfIncident = '';
                                $requestType = 'Enquiry';
                            }
                        } else {
                            // Email specific logic
                            $requestType = $value->request_type;
                            if ($requestType == 'enquiry') {
                                $typeOfIncident = '';
                                $requestType = 'enquiry';
                            } elseif ($requestType == 'service') {
                                if ($value->type_of_incident == 'service_it') {
                                    $typeOfIncident = 'IT Service';
                                    $requestType = 'Service';
                                } elseif ($value->type_of_incident == 'service_business') {
                                    $typeOfIncident = 'Business Service';
                                    $requestType = 'Service';
                                }
                            } elseif ($requestType == 'incident') {
                                if ($value->type_of_incident == 'incident_it') {
                                    $typeOfIncident = 'IT Incident';
                                    $requestType = 'Incident';
                                } elseif ($value->type_of_incident == 'incident_business') {
                                    $typeOfIncident = 'Business Incident';
                                    $requestType = 'Incident';
                                }
                            } elseif ($requestType == '' || $requestType == null) {
                                $typeOfIncident = '';
                                $requestType = '';
                            }
                        }

                        // Format case status
                        $subStatus = $value->case_status;
                        $caseStatusMap = [
                            'Open_Pending Input' => 'Pending Input',
                            'Closed_Closed' => 'Closed',
                            'Open_Assigned' => 'Assigned',
                            'Closed_Cancelled_Eaduan' => 'Cancelled by eAduan',
                            'Closed_Verified_Eaduan' => 'Closed by eAduan',
                            'Closed_Rejected_Eaduan' => 'Rejected by eAduan',
                            'Pending_User_Verification' => 'Pending User Verification',
                            'Closed_Rejected' => 'Rejected',
                            'Open_New' => 'New',
                            'Closed_Duplicate' => 'Duplicate',
                            'Open_Pending_Approval' => 'Pending Approval',
                            'Closed_Approved' => 'Approved',
                            'In_Progress' => 'In Progress',
                            'Open_Resolved' => 'Resolved'
                        ];
                        $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                        // Format case info
                        $infoC = $value->case_info;
                        $caseInfoMap = [
                            'info_completed' => 'Complete',
                            'info_notcompleted' => 'Pending More Info',
                            'rejected' => 'Rejected',
                            '' => ''
                        ];
                        $caseInfoC = $caseInfoMap[$infoC] ?? $infoC;

                        // Determine who picked up the case
                        $pickupBy = '';
                        if ($value->csName == null || $value->csName == '') {
                            $pickupBy = $value->csNama;
                        } else {
                            $pickupBy = $value->csName;
                        }

                        $formattedData[] = [
                            'counter' => ++$counter,
                            'case_id' => $value->case_id,
                            'case_number' => $value->case_number,
                            'case_state' => $value->case_state,
                            'case_status' => $caseStatus,
                            'case_info' => $caseInfoC,
                            'request_type' => $requestType,
                            'incident_type' => $typeOfIncident,
                            'sla_start' => $slaStart,
                            'sla_end' => $slaStop,
                            'respond_by' => $pickupBy
                        ];
                    }
                }
            }
        }

        return $formattedData;
    }

    private function formatNoResponseWithinSLAData($contactMode)
    {
        $CASE_CONTACT_MODE = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $list = $this->crmService->getDashboardCS();
        $formattedData = [];
        $counter = 0;
        $current = Carbon::now();

        foreach ($list as $value) {
            if ($value->contact_mode == $CASE_CONTACT_MODE && $value->cs_completed_datetime == null) {
                $availableSLAEnd = $value->cs_due_datetime;

                if ($availableSLAEnd > $current) {
                    $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                    $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

                    // Format case status
                    $subStatus = $value->case_status;
                    $caseStatusMap = [
                        'Open_Pending Input' => 'Pending Input',
                        'Closed_Closed' => 'Closed',
                        'Open_Assigned' => 'Assigned',
                        'Closed_Cancelled_Eaduan' => 'Cancelled by eAduan',
                        'Closed_Verified_Eaduan' => 'Closed by eAduan',
                        'Closed_Rejected_Eaduan' => 'Rejected by eAduan',
                        'Pending_User_Verification' => 'Pending User Verification',
                        'Closed_Rejected' => 'Rejected',
                        'Open_New' => 'New',
                        'Closed_Duplicate' => 'Duplicate',
                        'Open_Pending_Approval' => 'Pending Approval',
                        'Closed_Approved' => 'Approved',
                        'In_Progress' => 'In Progress',
                        'Open_Resolved' => 'Resolved'
                    ];
                    $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                    // Calculate remaining time
                    $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
                    $dateDiff = $current->diff(new DateTime($slaEnd));
                    $timeRemaining = '< ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                    $formattedData[] = [
                        'counter' => ++$counter,
                        'case_id' => $value->case_id,
                        'case_number' => $value->case_number,
                        'case_state' => $value->case_state,
                        'case_status' => $caseStatus,
                        'sla_start' => $slaStart,
                        'sla_end' => $slaStop,
                        'time_remaining' => $timeRemaining
                    ];
                }
            }
        }

        return $formattedData;
    }

    private function formatNoResponseWithinSLABySchedulerData($contactMode)
    {
        $CASE_CONTACT_MODE = ($contactMode == 'openportal') ? 'Open Portal' : 'Email';
        $list = $this->crmService->getDashboardCS();
        $formattedData = [];
        $counter = 0;
        $current = Carbon::now();

        foreach ($list as $value) {
            if ($value->contact_mode == $CASE_CONTACT_MODE && $value->cs_completed_datetime != null) {
                $availableSLAEnd = Carbon::parse($value->cs_due_datetime);
                $actualSLAEnd = Carbon::parse($value->cs_completed_datetime);

                if ($availableSLAEnd > $actualSLAEnd) {
                    // Check additional conditions
                    if (
                        $value->case_pickupdate == null && $value->request_type == '' &&
                        $value->case_info == '' && $value->case_status == 'Open_Pending Input'
                    ) {

                        $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
                        $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
                        $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

                        // Format case status
                        $subStatus = $value->case_status;
                        $caseStatusMap = [
                            'Open_Pending Input' => 'Pending Input',
                            'Closed_Closed' => 'Closed',
                            'Open_Assigned' => 'Assigned',
                            'Closed_Cancelled_Eaduan' => 'Cancelled by eAduan',
                            'Closed_Verified_Eaduan' => 'Closed by eAduan',
                            'Closed_Rejected_Eaduan' => 'Rejected by eAduan',
                            'Pending_User_Verification' => 'Pending User Verification',
                            'Closed_Rejected' => 'Rejected',
                            'Open_New' => 'New',
                            'Closed_Duplicate' => 'Duplicate',
                            'Open_Pending_Approval' => 'Pending Approval',
                            'Closed_Approved' => 'Approved',
                            'In_Progress' => 'In Progress',
                            'Open_Resolved' => 'Resolved'
                        ];
                        $caseStatus = $caseStatusMap[$subStatus] ?? $subStatus;

                        $slaEnd = Carbon::parse($value->cs_completed_datetime)->format("Y-m-d H:i:s");
                        $dateDiff = $current->diff(new DateTime($slaEnd));
                        $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                        $formattedData[] = [
                            'counter' => ++$counter,
                            'case_id' => $value->case_id,
                            'case_number' => $value->case_number,
                            'case_state' => $value->case_state,
                            'case_status' => $caseStatus,
                            'sla_start' => $slaStart,
                            'sla_end' => $slaStop,
                            'scheduler_action' => $slaStopbyScheduler,
                            'time_remaining' => $timeRemaining
                        ];
                    }
                }
            }
        }

        return $formattedData;
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->selectedType = null;
    }

    private function getDashboardTopEnqSubCategory()
    {
        return $this->crmService->getDashboardTopEnqSubCategory();
    }

    private function getDashboardTopIncServSubCategory()
    {
        return $this->crmService->getDashboardTopIncServSubCategory();
    }

    private function getDashboardCRMCS()
    {
        $current = Carbon::now();
        $processDate = Carbon::now()->toDateString();
        $todayDate = $current->format('d/m/Y');

        $countTotalCasesfromOpenPortal = 0;
        $countOpenPortalAssignedWithinSLA = 0;
        $countOpenPortalPendingResponseWithinSLA = 0;
        $countOpenPortalPendingRespondTakenbyScheduler = 0;
        $countOpenPortalAssignedExceedSLA = 0;
        $countOpenPortalPendingResponseExceedSLA = 0;
        $countOpenPortalWithinSLA = 0;
        $countOpenPortalExceedSLA = 0;
        $countOpenPortalRespondWithinSLA = 0;

        $countTotalCasesfromEmail = 0;
        $countEmailAssignedWithinSLA = 0;
        $countEmailPendingResponseWithinSLA = 0;
        $countEmailPendingRespondTakenbyScheduler = 0;
        $countEmailAssignedExceedSLA = 0;
        $countEmailPendingResponseExceedSLA = 0;
        $countEmailWithinSLA = 0;
        $countEmailRespondWithinSLA = 0;
        $countEmailExceedSLA = 0;

        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';

        $listCase = $this->crmService->getDashboardCS(null, null, $processDate);

        if (count($listCase) > 0) {
            foreach ($listCase as $cases) {
                $processDate = Carbon::now()->toDateString();
                $csCompleted = Carbon::parse($cases->cs_completed_datetime)->addHours(8)->format("Y-m-d H:i:s");
                $csDue = Carbon::parse($cases->cs_due_datetime)->addHours(8)->format("Y-m-d H:i:s");
                $csPickup = Carbon::parse($cases->case_pickupdate)->addHours(8)->format("Y-m-d H:i:s");

                if ($cases->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                    if ($cases->cs_completed_datetime != null) {
                        if ($csCompleted <= $csDue) {
                            if ($cases->case_pickupdate == null && $cases->request_type == '' && $cases->case_info == '' && $cases->case_status == 'Open_Pending Input') {
                                $countOpenPortalPendingRespondTakenbyScheduler++;
                            }
                            $countOpenPortalAssignedWithinSLA++;
                        } else {
                            $countOpenPortalAssignedExceedSLA++;
                        }
                    }
                    if ($cases->cs_completed_datetime == null) {
                        if ($csDue > $current) {
                            $countOpenPortalPendingResponseWithinSLA++;
                        }
                        if ($csDue < $current) {
                            $countOpenPortalPendingResponseExceedSLA++;
                        }
                    }
                    $countTotalCasesfromOpenPortal++;
                    $countOpenPortalRespondWithinSLA = $countTotalCasesfromOpenPortal - ($countOpenPortalPendingResponseWithinSLA + $countOpenPortalPendingRespondTakenbyScheduler + $countOpenPortalAssignedExceedSLA + $countOpenPortalPendingResponseExceedSLA);
                    $countOpenPortalWithinSLA = $countTotalCasesfromOpenPortal - ($countOpenPortalPendingResponseExceedSLA + $countOpenPortalAssignedExceedSLA);
                    $countOpenPortalExceedSLA = $countTotalCasesfromOpenPortal - $countOpenPortalWithinSLA;
                }

                if ($cases->contact_mode == $CASE_CONTACT_MODE_EMAIL) {
                    if ($cases->cs_completed_datetime != null) {
                        if ($csCompleted <= $csDue) {
                            if ($csPickup == null && $cases->request_type == '' && $cases->case_info == '' && $cases->case_status == 'Open_Pending Input') {
                                $countEmailPendingRespondTakenbyScheduler++;
                            }
                            $countEmailAssignedWithinSLA++;
                        } else {
                            $countEmailAssignedExceedSLA++;
                        }
                    }
                    if ($cases->cs_completed_datetime == null) {
                        if ($csDue > $current) {
                            $countEmailPendingResponseWithinSLA++;
                        }
                        if ($csDue < $current) {
                            $countEmailPendingResponseExceedSLA++;
                        }
                    }
                    $countTotalCasesfromEmail++;
                    $countEmailRespondWithinSLA = $countTotalCasesfromEmail - ($countEmailPendingResponseWithinSLA + $countEmailPendingRespondTakenbyScheduler + $countEmailAssignedExceedSLA + $countEmailPendingResponseExceedSLA);
                    $countEmailWithinSLA = $countTotalCasesfromEmail - ($countEmailPendingResponseExceedSLA + $countEmailAssignedExceedSLA);
                    $countEmailExceedSLA = $countTotalCasesfromEmail - $countEmailWithinSLA;
                }
            }
        }

        return [
            'todayDate' => $todayDate,
            'openPortal' => [
                'totalCases' => $countTotalCasesfromOpenPortal,
                'withinSLA' => $countOpenPortalWithinSLA,
                'exceedSLA' => $countOpenPortalExceedSLA,
                'assignedExceedSLA' => $countOpenPortalAssignedExceedSLA,
                'pendingResponseExceedSLA' => $countOpenPortalPendingResponseExceedSLA,
                'respondWithinSLA' => $countOpenPortalRespondWithinSLA,
                'pendingResponseWithinSLA' => $countOpenPortalPendingResponseWithinSLA,
                'pendingRespondTakenbyScheduler' => $countOpenPortalPendingRespondTakenbyScheduler
            ],
            'email' => [
                'totalCases' => $countTotalCasesfromEmail,
                'withinSLA' => $countEmailWithinSLA,
                'exceedSLA' => $countEmailExceedSLA,
                'assignedExceedSLA' => $countEmailAssignedExceedSLA,
                'pendingResponseExceedSLA' => $countEmailPendingResponseExceedSLA,
                'respondWithinSLA' => $countEmailRespondWithinSLA,
                'pendingResponseWithinSLA' => $countEmailPendingResponseWithinSLA,
                'pendingRespondTakenbyScheduler' => $countEmailPendingRespondTakenbyScheduler
            ]
        ];
    }

    private function getDashboardPendingResponse()
    {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $countPendingResponseCSOpenPortal = 0;
        $countPendingResponseCSEmail = 0;

        $listCase = $this->crmService->getDashboardPendingResponse();

        if (count($listCase) > 0) {
            foreach ($listCase as $cases) {
                if ($cases->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                    $countPendingResponseCSOpenPortal++;
                }

                if ($cases->contact_mode == $CASE_CONTACT_MODE_EMAIL) {
                    $countPendingResponseCSEmail++;
                }
            }
        }

        $hasAlerts = ($countPendingResponseCSOpenPortal > 0 || $countPendingResponseCSEmail > 0);

        return [
            'openPortal' => [
                'count' => $countPendingResponseCSOpenPortal,
                'hasAlert' => $countPendingResponseCSOpenPortal > 0
            ],
            'email' => [
                'count' => $countPendingResponseCSEmail,
                'hasAlert' => $countPendingResponseCSEmail > 0
            ],
            'hasAlerts' => $hasAlerts
        ];
    }

    private function getCrmSchedulerEmailInbound()
    {
        $data = $this->crmService->getLatestJobQue('Check Inbound Mailboxes');
        $current = Carbon::now();

        $executeTime = Carbon::parse($data->execute_time)->addHours(8)->format("Y-m-d H:i:s");
        $dateDiff = $current->diff(new DateTime($executeTime));

        $isGood = $dateDiff->i < 5;

        return [
            'id' => $data->id,
            'name' => $data->name,
            'executeTime' => $executeTime,
            'status' => $data->status,
            'resolution' => $data->resolution,
            'message' => $data->message,
            'isGood' => $isGood
        ];
    }

    public function render()
    {
        return view('livewire.crm.cs-cases', [
            'topEnqSubCategories' => $this->topEnqSubCategories,
            'topIncServSubCategories' => $this->topIncServSubCategories,
            'dashboardCSData' => $this->dashboardCSData,
            'pendingResponseData' => $this->pendingResponseData,
            'emailInboundData' => $this->emailInboundData
        ]);
    }
}