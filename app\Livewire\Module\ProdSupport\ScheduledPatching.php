<?php

namespace App\Livewire\Module\ProdSupport;

use Livewire\Component;

class ScheduledPatching extends Component
{
    public $canCreate = true;
    public $canUpdate = true;
    public $canDelete = true;
    public $patches = [];

    public function mount()
    {
        $this->patches = [
            [
                'id' => 1,
                'no' => 'SP001',
                'date_porting' => '2023-10-15',
                'crm_number' => 'CRM12345',
                'redmine_number' => 'RM7890',
                'module' => 'Inventory Management',
                'problem_description' => 'System crashes when updating large inventory batches',
                'problem_type' => 'System Error',
                'group_type' => 'Critical',
                'name' => '<PERSON>',
                'endorsed_by' => '<PERSON>',
                'endorsement_date' => '2023-10-10',
                'status' => 'Pending',
                'total_valid_cr' => 3,
                'total_valid_script' => 2,
                'created' => '2023-10-05',
                'helpdesk' => 'HD-2023-001'
            ]
        ];

        $this->dispatch('refresh-patches-table');
    }

    public function invokeFromUI()
    {
        // Example
        session()->flash('message', 'Successfully invoked!');

        $this->dispatch('alert', [
            'type' => 'success',
            'message' => 'Action successfully invoked!'
        ]);

        $this->dispatch('refresh-patches-table');
    }

    public function render()
    {
        return view('livewire.prod-support.scheduled-patching', [
            'patches' => $this->patches
        ]);
    }
}