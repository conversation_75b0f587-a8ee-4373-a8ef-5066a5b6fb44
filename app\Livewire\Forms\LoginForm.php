<?php

namespace App\Livewire\Forms;

use Livewire\Form;

class LoginForm extends Form
{
    public string $user_name = '';
    public string $password = '';
    public bool $remember = false;

    public function rules(): array
    {
        return [
            'user_name' => 'required|string|max:255',
            'password' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'user_name.required' => 'Username is required',
            'user_name.max' => 'Username cannot exceed 255 characters',
            'password.required' => 'Password is required',
        ];
    }

    public function resetForm(): void
    {
        $this->user_name = '';
        $this->password = '';
        $this->remember = false;
    }
}