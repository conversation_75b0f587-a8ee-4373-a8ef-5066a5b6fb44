<?php

namespace App\Livewire\AppSetting;

use Auth;
use Livewire\Component;
use App\Models\Permission;
use App\Livewire\Forms\AppSetting\PermissionForm;

class Permissions extends Component
{
    public PermissionForm $permissionForm;
    public $permissionIdToDelete = null;

    // Modal state properties
    public $isModalOpen = false;
    public $isDeleteModalOpen = false;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    private function authorizeAction(string $action): void
    {
        if (!$this->userCan($action)) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-permissions-table');
    }

    public function openAddPermissionModal()
    {
        $this->authorizeAction('create');
        $this->permissionForm->resetForm();
        $this->isModalOpen = true;
        $this->dispatch('open-permission-modal');
    }

    public function openEditPermissionModal($id)
    {
        $this->authorizeAction('update');
        $permission = Permission::find($id);
        $this->permissionForm->edit($permission);
        $this->isModalOpen = true;
        $this->dispatch('open-permission-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->permissionForm->resetForm();
    }

    public function openDeletePermissionModal($id)
    {
        $this->authorizeAction('delete');
        $this->permissionIdToDelete = $id;
        $this->isDeleteModalOpen = true;
        $this->dispatch('open-delete-permission-modal');
    }

    public function closeDeleteModal()
    {
        $this->isDeleteModalOpen = false;
        $this->permissionIdToDelete = null;
    }

    public function savePermission()
    {
        $action = $this->permissionForm->permissionId ? 'update' : 'create';
        $this->authorizeAction($action);

        $this->permissionForm->validate();

        try {
            $permission = $this->permissionForm->save();
            $this->permissionForm->resetForm();
            $this->isModalOpen = false;
            session()->flash('message', 'Permission saved successfully!');
            $this->dispatch('permission-saved');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to save permissions: ' . $e->getMessage());
        }
    }

    public function executeDeletePermission()
    {
        $this->authorizeAction('delete');

        if ($this->permissionIdToDelete) {
            Permission::destroy($this->permissionIdToDelete);
            $this->permissionIdToDelete = null;
            $this->isDeleteModalOpen = false;
            session()->flash('message', 'Permission deleted successfully!');
            $this->dispatch('permission-deleted');
        }
    }

    // Keep these methods for backward compatibility
    public function editPermission($id)
    {
        $this->openEditPermissionModal($id);
    }

    public function confirmDeletePermission($id)
    {
        $this->openDeletePermissionModal($id);
    }

    public function resetForms()
    {
        $this->permissionForm->resetForm();
    }

    public function render()
    {
        return view('livewire.app-setting.permissions', [
            'permissions' => Permission::all(),
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}