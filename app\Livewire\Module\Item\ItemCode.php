<?php

namespace App\Livewire\Module\Item;

use App\Livewire\Forms\Item\ItemCodeSearchForm;
use App\Services\ItemService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;


class ItemCode extends Component
{
    protected ItemService $ItemService;

    public ItemCodeSearchForm $itemCodeSearchForm;

    // Public properties to store data
    public $listdata = []; 
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    // Properties for modal
    public $isModalOpen = false;
    public $modalTitle = '';
    public $modalType = '';
    public $modalFind = '';
    public $listItemColor = [];
    public $listItemType = [];
    public $listItemBrand = [];
    public $listItemMeasurement = [];
    public $modalData = [];
    public $isLoading = false;

    public function boot(ItemService $ItemService)
    {
        $this->itemService = $ItemService;
    }

    public function mount()
    {
        $this->carian = 'Today Created';
        $this->itemCodeSearchForm->carian = $this->carian;
        $this->search();
    }

    public function rendered()
    {
        $this->dispatch('refresh-item-table');
    } 

    public function search()
    {
        $this->itemCodeSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->itemCodeSearchForm->carian;
        $this->errorMessage = null;

        try {
            $list = null;
            $carianTemp = trim(strtoupper($this->carian));
            $carianLen = strlen($carianTemp);
            if($carianLen == 8 && ctype_digit($carianTemp) == true ){
                $list = $this->itemService->getItemDetail("UNSPSC_CODE", $carianTemp); 
             }else if($carianLen == 18 ){
                $list = $this->itemService->getItemDetail("EXTENSION_CODE", $carianTemp); 
             }else{
                $list = $this->itemService->getItemDetail("DEFAULT", '');
             } 
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 
 
    public function openItemModal($data, $title, $itemDetail = 'color')
    {
        $this->modalFind = $data;
        $this->modalTitle = $title;
        $this->modalType = $itemDetail;
        $this->isLoading = true;
        $this->isModalOpen = true;

        try {
            switch ($itemDetail) {
                case 'color':
                    $this->listItemColor = $this->itemService->getListItemColor() ?? [];
                    break;

                case 'type':
                    $this->listItemType = $this->itemService->getListItemTypeByUNSPSCID($data) ?? [];
                    break;

                case 'brand':
                    $this->listItemBrand = $this->itemService->getListItemBrandByUNSPSCID($data) ?? [];
                    break;

                case 'measurement':
                    $this->listItemMeasurement = $this->itemService->getListItemMeasurementByUNSPSCID($data) ?? [];
                    break;

                default:
                    throw new \Exception("Unknown modal type: {$itemDetail}");
            }
        } catch (\Exception $e) {
            $this->modalData = [
                'data' => $data,
                'status' => 'Error',
                'details' => 'An error occurred while fetching data: ' . $e->getMessage()
            ];
            $this->listItemColor = [];
            $this->listItemType = [];
            $this->listItemBrand = [];
            $this->listItemMeasurement = [];
        } finally {
            $this->isLoading = false;
        }

        // Dispatch event to initialize the modal
        $this->dispatch('open-document-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->modalFind = '';
    }

    public function render()
    {
        return view('livewire.item.item-code', [
            'listdata' => $this->listdata
        ]);
    }
}