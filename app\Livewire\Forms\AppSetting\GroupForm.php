<?php

namespace App\Livewire\Forms\AppSetting;

use Livewire\Form;
use App\Models\Group;

class GroupForm extends Form
{
    public $groupId;
    public $groupName = '';
    public $groupDescription = '';
    public $selectedGroupUsers = [];
    public $selectedGroupRoles = [];

    public function rules()
    {
        return [
            'groupName' => 'required|unique:groups,name,' . $this->groupId,
            'groupDescription' => 'nullable',
        ];
    }

    public function save()
    {
        $groupData = [
            'name' => $this->groupName,
            'description' => $this->groupDescription,
        ];

        if ($this->groupId) {
            $group = Group::find($this->groupId);
            $group->update($groupData);
        } else {
            $group = Group::create($groupData);
        }

        $group->users()->sync($this->selectedGroupUsers);
        $group->roles()->sync($this->selectedGroupRoles);

        return $group;
    }

    public function edit(Group $group)
    {
        $this->groupId = $group->id;
        $this->groupName = $group->name;
        $this->groupDescription = $group->description;
        $this->selectedGroupUsers = $group->users->pluck('id')->toArray();
        $this->selectedGroupRoles = $group->roles->pluck('id')->toArray();
    }

    public function resetForm()
    {
        $this->groupId = null;
        $this->groupName = '';
        $this->groupDescription = '';
        $this->selectedGroupUsers = [];
        $this->selectedGroupRoles = [];
    }
}