<?php

namespace App\Livewire\Module\QuotationTender;

use App\Models\External\EpSupportMonitoringQtEp;
use App\Services\FulfilmentService;
use App\Services\SourcingService;
use Auth;
use Livewire\Component;
use Carbon\Carbon;

class Dashboard extends Component
{
    protected FulfilmentService $fulfilmentService;
    protected SourcingService $sourcingService;

    // Properties for holding data to be passed to the view
    public $qtPublishedData;
    public $qtClosingData;
    public $qtPendingReschedulePublicationData;
    public $qtPendingRescheduleProposalClosingDateData;
    public $qtAccumulatedPublishedData;
    public $qtAccumulatedClosingData;
    public $colorFontClassPublished;
    public $colorFontClassClosing;
    public $clrFontTotalQtPendingReschedulePublication;
    public $clrFontTotalQtPendingRescheduleProposalClosingDate;

    public $qtClosedTooEarlyList;
    public $qtPublishedByDayList;
    public $qtClosingByDayList;

    // For modal data
    public $selectedDate;
    public $modalData;
    public $modalTitle;
    public $isModalOpen = false;
    public $isLoading = false;
    public $modalType = '';

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.qt.dashboard', $action);
    }

    public function boot(FulfilmentService $fulfilmentService, SourcingService $sourcingService)
    {
        $this->fulfilmentService = $fulfilmentService;
        $this->sourcingService = $sourcingService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $this->loadMonitoringData();
        $this->loadQtClosedTooEarly();
        $this->loadQtPublishedByDay();
        $this->loadQtClosingByDay();
    }

    public function rendered()
    {
        $this->dispatch('refresh-qt-tables');
    }

    private function loadMonitoringData()
    {
        // Get data for QT monitoring
        $listQtPublished = $this->fulfilmentService->getListQtPublished();
        $this->qtPublishedData = count($listQtPublished);

        $listQtClosing = $this->fulfilmentService->getListQtClosing();
        $this->qtClosingData = count($listQtClosing);

        $listQtPendingReschedulePublication = $this->fulfilmentService->getListQtPendingReschedulePublication();
        $this->qtPendingReschedulePublicationData = count($listQtPendingReschedulePublication);

        $listQtPendingRescheduleProposalClosingDate = $this->fulfilmentService->getListQtPendingRescheduleProposalClosingDate();
        $this->qtPendingRescheduleProposalClosingDateData = count($listQtPendingRescheduleProposalClosingDate);

        $this->qtAccumulatedPublishedData = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_PUBLISHED')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        $this->qtAccumulatedClosingData = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_CLOSING')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        // Set color classes based on conditions
        $this->colorFontClassPublished = "text-info";
        $this->colorFontClassClosing = "text-info";

        if (Carbon::now()->hour >= 12) {
            if ($this->qtPublishedData > 0) {
                $this->colorFontClassPublished = "text-danger";
            } else {
                $this->colorFontClassPublished = "text-success";
            }

            if ($this->qtClosingData > 0) {
                $this->colorFontClassClosing = "text-danger";
            } else {
                $this->colorFontClassClosing = "text-success";
            }
        }

        $this->clrFontTotalQtPendingReschedulePublication = "text-success";
        $this->clrFontTotalQtPendingRescheduleProposalClosingDate = "text-success";

        if ($this->qtPendingReschedulePublicationData > 0) {
            $this->clrFontTotalQtPendingReschedulePublication = "text-danger";
        }

        if ($this->qtPendingRescheduleProposalClosingDateData > 0) {
            $this->clrFontTotalQtPendingRescheduleProposalClosingDate = "text-danger";
        }
    }

    private function loadQtClosedTooEarly()
    {
        $this->qtClosedTooEarlyList = $this->sourcingService->getDashboardQtListStatusClosedTooEarly();
    }

    private function loadQtPublishedByDay()
    {
        $this->qtPublishedByDayList = $this->fulfilmentService->getListQtPublishedByDay();
    }

    private function loadQtClosingByDay()
    {
        $this->qtClosingByDayList = $this->fulfilmentService->getListQtClosingByDay();
    }

    public function loadPublishedByMinistry($date)
    {
        $this->selectedDate = $date;
        $this->modalTitle = 'List Published QT By Ministry';
        $this->modalType = 'published';
        $this->isLoading = true;
        $this->isModalOpen = true;

        try {
            $this->modalData = $this->fulfilmentService->getListQtPublishedByMinistry($date);
        } catch (\Exception $e) {
            $this->modalData = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-qt-details-modal');
    }

    public function loadClosingByMinistry($date)
    {
        $this->selectedDate = $date;
        $this->modalTitle = 'List Closing QT By Ministry';
        $this->modalType = 'closing';
        $this->isLoading = true;
        $this->isModalOpen = true;

        try {
            $this->modalData = $this->fulfilmentService->getListQtClosingByMinistry($date);
        } catch (\Exception $e) {
            $this->modalData = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-qt-details-modal');
    }

    // Add close modal method
    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->selectedDate = null;
    }

    public function render()
    {
        return view('livewire.quotation-tender.dashboard', [
            'qtPublishedData' => $this->qtPublishedData,
            'qtClosingData' => $this->qtClosingData,
            'qtPendingReschedulePublicationData' => $this->qtPendingReschedulePublicationData,
            'qtPendingRescheduleProposalClosingDateData' => $this->qtPendingRescheduleProposalClosingDateData,
            'qtAccumulatedPublishedData' => $this->qtAccumulatedPublishedData,
            'qtAccumulatedClosingData' => $this->qtAccumulatedClosingData,
            'colorFontClassPublished' => $this->colorFontClassPublished,
            'colorFontClassClosing' => $this->colorFontClassClosing,
            'clrFontTotalQtPendingReschedulePublication' => $this->clrFontTotalQtPendingReschedulePublication,
            'clrFontTotalQtPendingRescheduleProposalClosingDate' => $this->clrFontTotalQtPendingRescheduleProposalClosingDate,
            'qtClosedTooEarlyList' => $this->qtClosedTooEarlyList,
            'qtPublishedByDayList' => $this->qtPublishedByDayList,
            'qtClosingByDayList' => $this->qtClosingByDayList,
            'modalData' => $this->modalData,
            'modalTitle' => $this->modalTitle
        ]);
    }
}