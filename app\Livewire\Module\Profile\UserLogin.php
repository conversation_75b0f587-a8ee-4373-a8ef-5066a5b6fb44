<?php

namespace App\Livewire\Module\Profile;

use App\Livewire\Forms\Profile\UserLoginSearchForm;
use App\Services\EpWebService;
use App\Services\SupplierService;
use Auth;
use Livewire\Attributes\Url;
use Livewire\Component;

use DB;
use Log;

class UserLogin extends Component
{
    public UserLoginSearchForm $userLoginSearchForm;
    #[Url]
    public $searchValue = null;

    // Data properties
    public $userData;
    public $history;
    public $organisasi;
    public $isGpkiValid = false;
    public $gpkiLastSigning = null;
    public $loginId = '';

    // Role-based flags
    public $hasAdvancedRole = false;
    public $hasPatchRole = false;

    protected EpWebService $epWebService;
    protected SupplierService $supplierService;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.profile.user-login', $action);
    }

    public function boot(EpWebService $epWebService, SupplierService $supplierService)
    {
        $this->epWebService = $epWebService;
        $this->supplierService = $supplierService;
        $this->userData = collect([]);

        // Set role flags
        $user = Auth::user();
        $this->hasAdvancedRole = $user && $user->hasRole('role_adv_ep');
        $this->hasPatchRole = $user && $user->hasRole('role_patch_ep');
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($this->searchValue) {
            $this->userLoginSearchForm->search = $this->searchValue;
            $this->searchUserLogin();
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-user-status-table');
        $this->dispatch('refresh-organization-history-table');
    }

    public function searchUserLogin()
    {
        $this->userLoginSearchForm->validate();
        $this->searchValue = $this->userLoginSearchForm->search;

        // Reset values
        $this->userData = collect([]);
        $this->history = '';
        $this->organisasi = '';
        $this->isGpkiValid = false;

        $this->loginId = $this->userLoginSearchForm->search;

        if (strlen($this->loginId) == 0) {
            return;
        }

        $user = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU')
            ->join('PM_PARAMETER_DESC as PMDESC', 'PMDESC.PARAMETER_ID', 'PMU.ORG_TYPE_ID')
            ->leftJoin('PM_LOGIN_HISTORY as LH', 'LH.USER_ID', 'PMU.USER_ID')
            ->where('PMDESC.LANGUAGE_CODE', '=', 'en')
            ->where('login_id', $this->loginId)
            ->select('PMU.*')
            ->addSelect('PMDESC.code_name')
            ->addSelect('LH.login_date')
            ->first();

        // Only fetch SSO data if user has advanced role
        $userSso = null;
        if ($this->hasAdvancedRole) {
            try {
                $userSso = DB::connection('oracle_sso')->table('USR')
                    ->whereRaw("lower(usr_login) = lower('$this->loginId')")
                    ->first();
            } catch (\Exception $exc) {
                Log::error("ERROR Connection DB SSO get Loging ID $this->loginId >>> " . $exc->getMessage());
                Log::error($exc->getTraceAsString());
            }
        }
        $this->userData->put('usersso', $userSso);

        if ($user != null) {
            // Only calculate eAduan login token if user has advanced role
            if ($this->hasAdvancedRole) {
                $user->eaduan_login = $this->getTokenEaduan($this->loginId);
            }

            $this->userData->put('userdata', $user);
            $this->checkGpkiLastSigning($user->user_id);
            $objResult = $this->epWebService->wsStatusUserLogin($this->loginId);

            $this->isGpkiValid = false;

            if ($objResult && isset($objResult['result'])) {
                // Extract role lists from the decoded JSON data
                $pmRoleList = $this->convertStringToArray($objResult['result']['PmRole']);
                $liferayRoleList = $this->convertStringToArray($objResult['result']['LiferayRole']);
                $oimRoleList = $this->convertStringToArray($objResult['result']['OimRole']);

                // Convert role lists to arrays
                $pmRoles = collect($pmRoleList);

                $this->userData->put('userstatus', $objResult);

                $liferayRoles = collect($liferayRoleList);
                $oimRoles = collect($oimRoleList);

                // Only process missing/extra roles if user has advanced role
                if ($this->hasAdvancedRole) {
                    // Find roles missing in LiferayRole compared to PmRole
                    $rolesMissingInLiferay = $pmRoles->diff($liferayRoles)->all();
                    // Find roles missing in OimRole compared to PmRole
                    $rolesMissingInOim = $pmRoles->diff($oimRoles)->all();

                    // Find extra roles in Liferay compared to PmRole
                    $extraRolesInLiferay = $liferayRoles->diff($pmRoles)->all();
                    // Find extra roles in OimRole compared to PmRole
                    $extraRolesInOim = $oimRoles->diff($pmRoles)->all();

                    // Add to userData
                    $this->userData->put('ExtraRoleLiferay', $extraRolesInLiferay);
                    $this->userData->put('ExtraRoleOim', $extraRolesInOim);
                    $this->userData->put('MissingRoleLiferay', $rolesMissingInLiferay);
                    $this->userData->put('MissingRoleOim', $rolesMissingInOim);
                }

                // gpki roles - always process regardless of role
                $gpkiRole = collect(['CT_APPROVER', 'BPK_CC_APPROVER', 'RN_APPROVER', 'FL_APPROVER', 'ACKNOWLEDGE_OFFICER', 'PAY_OFFICER', 'BPK_CQ_APPROVER', 'BPK_PQ_APPROVER', 'COMMITTEE_APPROVER', 'PUBLICATION_APPROVER', 'SC_CHAIRPERSON', 'EC_CHAIRPERSON', 'OC_CHAIRPERSON', 'TEC_CHAIRPERSON', 'FEC_CHAIRPERSON', 'QCA_CHAIRPERSON', 'QCB_CHAIRPERSON', 'PPB_CHAIRPERSON', 'FPB_CHAIRPERSON', 'FPB_CHAIRPERSON']);

                $this->isGpkiValid = $pmRoles->contains(function ($value, $key) use ($gpkiRole) {
                    return $gpkiRole->contains($value);
                });
            }

            $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER_ORG as PMUO');
            $query->where('PMUO.USER_ID', $user->user_id);
            $query->select('PMUO.org_profile_id');

            if ($user->org_type_id == 15) {
                $query->join('SM_SUPPLIER as SUPP', 'SUPP.SUPPLIER_ID', 'PMUO.ORG_PROFILE_ID');
                $query->select('SUPP.SUPPLIER_ID AS ORG_PROFILE_ID', 'SUPP.COMPANY_NAME AS NAMA_ORGANISASI', 'SUPP.REG_NO as SSM_NO_ORG_PROFILE_ID', 'SUPP.EP_NO as EP_NO_ORG_CODE', 'SUPP.RECORD_STATUS as RECORD_STATUS', 'SUPP.ESTABLISH_DATE as EFF_ESTABLISH_DATE');
                $query->addSelect('SUPP.CREATED_DATE as CREATED_DATE', 'SUPP.CHANGED_DATE as CHANGED_DATE', 'PMUO.USER_ORG_ID', 'SUPP.record_status as org_validity_record_status');

                $this->history = $query->get();
                $this->organisasi = $query->where('SUPP.RECORD_STATUS', 1)->first();
            } else {
                $query->join('PM_ORG_VALIDITY as PMOV', 'PMOV.ORG_PROFILE_ID', 'PMUO.ORG_PROFILE_ID');
                $query->whereRaw("DECODE(to_number(to_char(PMOV.exp_date, 'j')),NULL,0,to_number(to_char(PMOV.exp_date, 'j'))) = (SELECT max(DECODE(to_number(to_char(exp_date, 'j')),NULL,0,to_number(to_char(exp_date, 'j')))) as exp_date_long FROM pm_org_validity  WHERE org_code = PMOV.org_code ) ");
                $query->select('PMOV.ORG_PROFILE_ID', 'PMOV.ORG_NAME as NAMA_ORGANISASI', 'PMOV.ORG_PROFILE_ID as SSM_NO_ORG_PROFILE_ID', 'PMOV.ORG_CODE as EP_NO_ORG_CODE', 'PMOV.RECORD_STATUS as PMOV_RECORD_STATUS', 'PMOV.EFF_DATE as EFF_ESTABLISH_DATE');
                $query->addSelect('PMOV.CREATED_DATE as CREATED_DATE', 'PMOV.CHANGED_DATE as CHANGED_DATE', 'PMUO.record_status as record_status', 'PMUO.USER_ORG_ID', 'PMOV.record_status as org_validity_record_status');

                $this->history = $query->get();
                $this->organisasi = $query->where('PMUO.RECORD_STATUS', 1)->where('PMOV.RECORD_STATUS', 1)->first();
            }
        }
    }

    public function checkGpkiLastSigning($userId)
    {
        $resultData = $this->supplierService->getLatestGpkiSuccessSigning($userId);

        if (count($resultData) > 0) {
            $dateSigning = $resultData[0]->latest_signing;
            $this->gpkiLastSigning = strlen($dateSigning) > 0 ? $dateSigning : null;
        } else {
            $this->gpkiLastSigning = null;
        }
    }

    private static function getTokenEaduan($userName)
    {
        $privateKey = 'CRM-eP-NextGen-2018';
        $time = strtotime("now");
        $b64Time = base64_encode($time);
        $timeCode = '5555';

        $pKey = base64_encode(hash("sha256", $privateKey, true));
        $b64PKey = base64_encode($pKey);
        $b64LoginID = base64_encode($userName);
        $b64PKeyLoginID = base64_encode(base64_encode(base64_encode($b64LoginID . $b64PKey)));
        $token = base64_encode(base64_encode($b64PKeyLoginID . $time));

        $timeCodeKey = base64_encode(hash("sha256", $timeCode, true));
        $b64TimeCode = base64_encode($timeCodeKey);
        $tid = base64_encode(base64_encode($b64TimeCode . $b64Time));

        return "https://crm.eperolehan.gov.my/portal/?token=$token&tid=$tid";
    }

    private function convertStringToArray($valueByComma)
    {
        // Extract the string from the collection (assuming it's the first item)
        $rolesString = collect($valueByComma)->first();

        // Remove square brackets and whitespace from the string
        $rolesString = str_replace(['[', ']', ' '], '', $rolesString);

        // Explode the string into an array using commas as the delimiter
        $rolesArray = explode(',', $rolesString);

        // Trim whitespace from each role name in the array
        $rolesArray = array_map('trim', $rolesArray);

        // Return the converted array (you can use this array as needed)
        return $rolesArray;
    }

    public function render()
    {
        return view('livewire.profile.user-login', [
            'carian' => $this->loginId,
            'result' => $this->userData,
            'history' => $this->history,
            'organisasi' => $this->organisasi,
            'isGpkiValid' => $this->isGpkiValid,
            'hasAdvancedRole' => $this->hasAdvancedRole,
            'hasPatchRole' => $this->hasPatchRole,
            'gpkiLastSigning' => $this->gpkiLastSigning
        ]);
    }
}