<?php

namespace App\Livewire\Module\Profile;

use App\Livewire\Forms\Profile\UomSearchForm;
use App\Services\ProfileService;
use Livewire\Attributes\Url;
use Livewire\Component;
use Illuminate\Support\Facades\Response;

class Uom extends Component
{
    protected ProfileService $ProfileService;

    public UomSearchForm $uomSearchForm;

    // Public properties to store data
    public $listdata = [];
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public function boot(ProfileService $ProfileService)
    {
        $this->profileService = $ProfileService;
    }

    public function mount()
    {
        if ($this->carian) {
            $this->uomSearchForm->carian = $this->carian;
            $this->search();
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-uom-table');
    } 

    public function search()
    {
        $this->uomSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->uomSearchForm->carian;
        $this->errorMessage = null;

        try {
            $carianTemp = strtolower($this->carian);
            $list = $this->profileService->getListUomByUomName($carianTemp);
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 

    public function render()
    {
        return view('livewire.profile.uom', [
            'listdata' => $this->listdata
        ]);
    }

    public function exportToCsv()
    { 
        $csvFileName = 'uom.csv';
        $csvFile = fopen($csvFileName, 'w');
        $headers = ["ID", "Code", "Name", "Decimal Scale", "Created Date", "Changed Date"]; 
        
        fputcsv($csvFile, $headers);

        foreach ($this->listdata as $row) {
            // fputcsv($csvFile, (array) $row);
            fputcsv($csvFile, array($row->uom_id, $row->uom_code, $row->uom_name, $row->decimal_scale,
            $row->created_date, $row->changed_date));
        }

        fclose($csvFile);
 
        return Response::download(public_path($csvFileName));

    }
}