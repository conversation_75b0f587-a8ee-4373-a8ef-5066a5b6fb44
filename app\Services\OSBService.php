<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class OSBService
{
    public function checkSuccessReceiveCertSPKI($softcertRequestID, $icNo)
    {
        $service_code = "SPK-010";
        $total = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_2', $icNo)
            ->where('service_code', $service_code)
            ->count();
        $total2 = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_2', $icNo)
            ->where('service_code', $service_code)
            ->count();
        return $total + $total2;
    }

    public function checkSuccessSentSPKI($softcertRequestID, $icNo)
    {
        $service_code = ['SPK-020', 'SPK-050', 'SPK-060', 'SPK-030'];
        $total = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_3', $icNo)
            ->whereIN('service_code', $service_code)
            ->count();

        // Find in Schema NGEP_OSB
        $total2 = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_3', $icNo)
            ->whereIN('service_code', $service_code)
            ->count();

        return $total + $total2;
    }
}