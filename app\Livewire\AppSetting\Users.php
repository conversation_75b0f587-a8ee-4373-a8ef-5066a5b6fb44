<?php

namespace App\Livewire\AppSetting;

use Auth;
use Livewire\Component;
use App\Models\User;
use App\Livewire\Forms\AppSetting\UserForm;

class Users extends Component
{
    public UserForm $userForm;
    public $userIdToDelete = null;

    // Modal state properties
    public $isModalOpen = false;
    public $isDeleteModalOpen = false;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    private function authorizeAction(string $action): void
    {
        if (!$this->userCan($action)) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function rendered()
    {
        $this->dispatch('refresh-users-table');
    }

    public function openAddUserModal()
    {
        $this->authorizeAction('create');
        $this->userForm->resetForm();
        $this->isModalOpen = true;
        $this->dispatch('open-user-modal');
    }

    public function openEditUserModal($id)
    {
        $this->authorizeAction('update');
        $user = User::find($id);
        $this->userForm->edit($user);
        $this->isModalOpen = true;
        $this->dispatch('open-user-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->userForm->resetForm();
    }

    public function openDeleteUserModal($id)
    {
        $this->authorizeAction('delete');
        $this->userIdToDelete = $id;
        $this->isDeleteModalOpen = true;
        $this->dispatch('open-delete-user-modal');
    }

    public function closeDeleteModal()
    {
        $this->isDeleteModalOpen = false;
        $this->userIdToDelete = null;
    }

    public function saveUser()
    {
        $action = $this->userForm->userId ? 'update' : 'create';
        $this->authorizeAction($action);

        $this->userForm->validate();

        $this->userForm->save();
        $this->userForm->resetForm();
        $this->isModalOpen = false;
        session()->flash('message', 'User saved successfully!');
        $this->dispatch('user-saved');
    }

    public function executeDeleteUser()
    {
        $this->authorizeAction('delete');

        if ($this->userIdToDelete) {
            User::destroy($this->userIdToDelete);
            $this->userIdToDelete = null;
            $this->isDeleteModalOpen = false;
            session()->flash('message', 'User deleted successfully!');
            $this->dispatch('user-deleted');
        }
    }

    // Keep these methods for backward compatibility
    public function editUser($id)
    {
        $this->openEditUserModal($id);
    }

    public function confirmDeleteUser($id)
    {
        $this->openDeleteUserModal($id);
    }

    public function resetForms()
    {
        $this->userForm->resetForm();
    }

    public function render()
    {
        return view('livewire.app-setting.users', [
            'users' => User::all(),
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}