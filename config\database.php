<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for database operations. This is
    | the connection which will be utilized unless another connection
    | is explicitly specified when you execute a query / statement.
    |
    */

    'default' => env('DB_CONNECTION', 'sqlite'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Below are all of the database connections defined for your application.
    | An example configuration is provided for each database system which
    | is supported by Laravel. You're free to add / remove connections.
    |
    */

    'connections' => [

        'epss2' => [
            'driver' => 'mysql',
            'url' => env('DB_EPSS_URL'),
            'host' => env('DB_EPSS_HOST', '**************'),
            'port' => env('DB_EPSS_PORT', '3306'),
            'database' => env('DB_EPSS_DATABASE', 'epss'),
            'username' => env('DB_EPSS_USERNAME', 'ep_beta'),
            'password' => env('DB_EPSS_PASSWORD', 'cDc@2020'),
            'unix_socket' => env('DB_EPSS_SOCKET', ''),
            'charset' => env('DB_EPSS_CHARSET', 'utf8mb4'),
            'collation' => env('DB_EPSS_COLLATION', 'utf8mb4_general_ci'),
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DB_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
            'busy_timeout' => null,
            'journal_mode' => null,
            'synchronous' => null,
        ],

        'oracle_nextgen_rpt' => [
            'driver' => 'oracle',
            'tns' => env('DB_NEXTGEN_RPT_TNS', ''),
            'host' => env('DB_NEXTGEN_RPT_HOST', 'raceprpt-cluster-scan.eperolehan.com.my'),
            'port' => env('DB_NEXTGEN_RPT_PORT', '1521'),
            'database' => env('DB_NEXTGEN_RPT_DATABASE', 'EPRPT'),
            'service_name' => env('DB_NEXTGEN_RPT_DATABASE', 'EPRPT'),
            'username' => env('DB_NEXTGEN_RPT_USERNAME', 'NGEP_READ'),
            'password' => env('DB_NEXTGEN_RPT_PASSWORD', 'ng3p_r34d'),
            'charset' => env('DB_NEXTGEN_RPT_CHARSET', 'AL32UTF8'),
            'prefix' => env('DB_NEXTGEN_RPT_PREFIX', ''),

        ],

        'cdccrm' => [
            'driver' => 'mysql',
            'url' => env('DB_CRMEP_URL'),
            'host' => env('DB_CRMEP_HOST', '***************'),
            'port' => env('DB_CRMEP_PORT', '3306'),
            'database' => env('DB_CRMEP_DATABASE', 'cdccrm_v2'),
            'username' => env('DB_CRMEP_USERNAME', 'crm_user'),
            'password' => env('DB_CRMEP_PASSWORD', 'cRm@2017'),
            'unix_socket' => env('DB_CRMEP_SOCKET', ''),
            'charset' => env('DB_CRMEP_CHARSET', 'utf8mb4'),
            'collation' => env('DB_CRMEP_COLLATION', 'utf8mb4_unicode_ci'),
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        // sso readonly
        'oracle_sso' => [
            'driver' => 'oracle',
            'tns' => env('DB_NEXTGEN_SSO_TNS', ''),
            'host' => env('DB_NEXTGEN_SSO_HOST', '**************'),
            'port' => env('DB_NEXTGEN_SSO_PORT', '1521'),
            'database' => env('DB_NEXTGEN_SSO_DATABASE', 'ngepdbstb'),
            'service_name' => env('DB_NEXTGEN_SSO_DATABASE', 'ngepdbstb'),
            'username' => env('DB_NEXTGEN_SSO_USERNAME', 'ngep_read'),
            'password' => env('DB_NEXTGEN_SSO_PASSWORD', 'ng3p_r34d'),
            'charset' => env('DB_NEXTGEN_SSO_CHARSET', 'AL32UTF8'),
            'prefix' => env('DB_NEXTGEN_SSO_PREFIX', ''),

        ],

        'mysql_ep_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_SUPPORT_HOST', ''),
            'port' => env('DB_MYSQL_EP_SUPPORT_PORT', ''),
            'database' => env('DB_MYSQL_EP_SUPPORT_DATABASE', ''),
            'username' => env('DB_MYSQL_EP_SUPPORT_USERNAME', ''),
            'password' => env('DB_MYSQL_EP_SUPPORT_PASSWORD', ''),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes' => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_DATABASE', 'cdccrm_v2'),
            'username' => env('DB_MYSQL_CRM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_PASSWORD', 'cDccRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes' => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_poms' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_POMS_HOST', '**************'),
            'port' => env('DB_MYSQL_POMS_PORT', '3306'),
            'database' => env('DB_MYSQL_POMS_DATABASE', 'cdc_poms'),
            'username' => env('DB_MYSQL_POMS_USERNAME', 'poms_user'),
            'password' => env('DB_MYSQL_POMS_PASSWORD', 'cdcPoms@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes' => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_cms' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CMS_HOST', '**************'),
            'port' => env('DB_MYSQL_CMS_PORT', '3306'),
            'database' => env('DB_MYSQL_CMS_DATABASE', 'cdccms'),
            'username' => env('DB_MYSQL_CMS_USERNAME', 'epcmsuser'),
            'password' => env('DB_MYSQL_CMS_PASSWORD', 'cDc@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes' => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run on the database.
    |
    */

    'migrations' => [
        'table' => 'migrations',
        'update_date_on_publish' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as Memcached. You may define your connection settings here.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
            'persistent' => env('REDIS_PERSISTENT', false),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
