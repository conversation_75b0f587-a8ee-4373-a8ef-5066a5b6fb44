<?php

namespace App\Livewire\Module\RevenueReport;

use App\Livewire\Forms\RevenueReport\TodayTransSearchForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;

class TodayTrans extends Component
{
    protected RevenueService $RevenueService;
    public TodayTransSearchForm $todayTransSearchForm;

    // Public properties to store data
    public $listdata = [];
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public function boot(RevenueService $RevenueService)
    {
        $this->revenueService = $RevenueService;
    }


    public function mount()
    {
        $this->carian = Carbon::now()->format('Y-m-d');
        $this->todayTransSearchForm->carian = $this->carian;
        $this->search();
    }

    public function search()
    {
        $this->todayTransSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->todayTransSearchForm->carian;
        $this->errorMessage = null;
        try {
            $dateTemp = Carbon::parse($this->carian)->format('Y-m-d');
            $list = $this->revenueService->getStatPVTRDailyToday($dateTemp);
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 

    public function render()
    {
        return view('livewire.revenue-report.today-trans', [
            'listdata' => $this->listdata
        ]);
    }
}