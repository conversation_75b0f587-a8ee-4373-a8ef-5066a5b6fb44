<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRoutePermission
{
    /**
     * Map HTTP methods to permission actions
     */
    protected $methodToActionMap = [
        'GET' => 'read',
        'POST' => 'create',
        'PUT' => 'update',
        'PATCH' => 'update',
        'DELETE' => 'delete',
    ];

    /**
     * Check if the user has permission to access the current route.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $routeName = $request->route()->getName();

        if (!$user || !$routeName) {
            abort(403, 'You do not have permission to access this page.');
        }

        // Determine the action based on the HTTP method
        $method = $request->method();
        $action = $this->methodToActionMap[$method] ?? 'read'; // Default to 'read' for unknown methods

        // Check if user has permission for this route and action
        if (!$user->canAccessRoute($routeName, $action)) {
            abort(403, 'You do not have permission to perform this action.');
        }

        return $next($request);
    }
}