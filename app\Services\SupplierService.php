<?php

namespace App\Services;

use App\Traits\EpTrait;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SupplierService
{
    use EpTrait;

    protected OSBService $osbService;
    protected ProfileService $profileService;

    public function __construct(OSBService $osbService, ProfileService $profileService)
    {
        $this->osbService = $osbService;
        $this->profileService = $profileService;
    }

    public function getSMSupplierUsersByIcNo($icno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->where('P.IDENTIFICATION_NO', $icno);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->orderBy('P.EP_ROLE');
        return $query->get();
    }

    public function getSMSupplierInfo($carian, $type)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as S');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        if ($type === 'ID') {
            $query->where('S.SUPPLIER_ID', $carian);
        } else if ($type === 'SUPPLIER_NAME' && strlen($carian) > 0) {
            $query->whereRaw("UPPER(S.COMPANY_NAME) =  '" . strtoupper($carian) . "'");
        } else if ($type === 'ePNo' && strlen($carian) > 0) {
            $query->where('S.EP_NO', $carian);
        } else if ($type === 'MOF' && strlen($carian) > 0) {
            $query->where('MA.MOF_NO', $carian);
        } else if ($type === 'ApplNo' && strlen($carian) > 0) {
            $query->leftJoin('SM_APPL as SA', 'SA.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID')
                ->where('SA.APPL_NO', $carian);
        } else {
            return null;  //Must return null, if not meet conditions.
        }
        $query->select('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');

        //dump($query->toSql());
        return $query->get();
    }

    public function getSMSupplierMofDetail($supplierId)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_MOF_ACCOUNT as M');
        $query->where('M.SUPPLIER_ID', $supplierId);
        $query->whereRaw('M.EXP_DATE IN ( select max(exp_date) from SM_MOF_ACCOUNT where SUPPLIER_ID = M.SUPPLIER_ID) ');
        $result = $query->get();
        if (count($result) > 0) {
            return $result[0];
        } else {
            return null;
        }
    }

    public function getSMSupplierUsersActiveBySupplierID($supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->where('S.SUPPLIER_ID', $supplierId);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));

        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        return $query->get();
    }

    public function populatePersonnelUserData(&$data)
    {
        $data->is_activate_key = false;
        if (strlen($data->p_ep_role) > 0 && strlen($data->p_email) > 0 && $data->p_record_status == 8) {

            $data->is_activate_key = true;
            $data->content_param = '';
            $contents = '';
            $data->link = 'Supplier Admin will able to resend activation link.';
            $data->activation_key = '';
            $data->is_sent = false;
            $data->activation_changed_date = null;
            if ($data->p_ep_role == 'SUPPLIER_TEMP' || $data->p_ep_role == 'BASIC_SUPPLIER_ADMIN' || $data->p_ep_role == 'MOF_SUPPLIER_ADMIN') {
                $recNotify = $this->getActivationLink($data->company_name, $data->reg_no, $data->p_identification_no, $data->p_email);
                if ($recNotify != null) {
                    $data->content_param = $recNotify->content_param;
                    $contents = explode("|||", $recNotify->content_param);
                    $data->link = $contents[3];
                    $data->activation_key = $contents[2];
                    $data->is_sent = $recNotify->is_sent;
                    $data->activation_changed_date = $recNotify->changed_date;
                }
            }
        }

        $data->u_record_status = $data->u_record_status . ' - ' . EpTrait::$RECORD_STATUS[$data->u_record_status];
        $data->supp_record_status = $data->s_record_status;
        $data->s_record_status = $data->s_record_status . ' - ' . EpTrait::$RECORD_STATUS[$data->s_record_status];
        $data->ma_record_status = $data->ma_record_status . ' - ' . EpTrait::$RECORD_STATUS[$data->ma_record_status];

        $data->p_is_softcert = $data->p_is_softcert . ' - ' . EpTrait::$SOFTCERT_STATUS[$data->p_is_softcert];
        $data->p_record_status = $data->p_record_status . ' - ' . EpTrait::$RECORD_STATUS[$data->p_record_status];

        $data->is_equity_owner = EpTrait::$YES_NO[$data->is_equity_owner];
        $data->is_authorized = EpTrait::$YES_NO[$data->is_authorized];
        $data->is_contact_person = EpTrait::$YES_NO[$data->is_contact_person];
        $data->is_contract_signer = EpTrait::$YES_NO[$data->is_contract_signer];
        $data->is_mgt = EpTrait::$YES_NO[$data->is_mgt];
        $data->is_director = EpTrait::$YES_NO[$data->is_director];
        $data->is_bumi = EpTrait::$YES_NO[$data->is_bumi];

        /** Get Softcert Request && (cert info if exist) * */
        $listSoftCert = $this->getListSoftCertRequest($data->ep_no, $data->user_id);
        if (count($listSoftCert) > 0) {
            foreach ($listSoftCert as $sCert) {
                $sCert->record_status = $sCert->record_status . ' - ' . EpTrait::$RECORD_STATUS[$sCert->record_status];
                $sCert->is_free = EpTrait::$YES_NO[$sCert->is_free];

                /** Checking in OSB * */
                $sCert->is_success_SPK010 = 0;
                $sCert->is_success_SPK020 = 0;
                if ($sCert->cert_serial_no == null) {
                    $sCert->is_success_SPK010 = $this->osbService->checkSuccessReceiveCertSPKI($sCert->softcert_request_id, $data->p_identification_no);
                    $sCert->is_success_SPK020 = $this->osbService->checkSuccessSentSPKI($sCert->softcert_request_id, $data->p_identification_no);
                }

                /** For Trustgate in List Migration * */
                $date = Carbon::parse($sCert->created_date);
                $dateNotTrustgateMigrate = Carbon::create(2017, 12, 31);
                $sCert->is_trustgate = false;
                if ($date->lte($dateNotTrustgateMigrate)) {
                    $sCert->is_trustgate = true;
                    $sCert->is_trustgate_data = false;
                    // $trustGateSoftData = $this->getTrustgateDetailInfo($data->mof_no, $data->identification_no);
                    // if ($trustGateSoftData) {
                    //     $sCert->is_trustgate_data = true;
                    //     $sCert->trustgate_expired_date = $trustGateSoftData->cert_expiry_date;
                    // }
                }

                $sCert->type_apply = 'ONLINE';
                $countHasLampiranOnline = $this->checkSoftcertIsONLINE($sCert->softcert_request_id, $data->user_id);
                if ($countHasLampiranOnline == 0) {
                    $sCert->type_apply = 'OFFLINE';
                }


                $sCert->is_expired = null;

                /* Checking is Expired? * */
                if (
                    $sCert->valid_to != null &&
                    Carbon::parse($sCert->valid_to)->lte(Carbon::now())
                ) {
                    $sCert->is_expired = true;
                } else {
                    $sCert->is_expired = false;
                }

                $sCert->is_active_cert = false;
                /* Checking active? * */
                if (
                    $sCert->valid_to != null &&
                    Carbon::parse($sCert->valid_to)->gte(Carbon::now())
                ) {
                    $sCert->is_active_cert = true;
                }
            }
        }
        $data->listSoftCert = $listSoftCert;

        /* Get Total Address Personnel */
        $totalAddressPersonnel = $this->getTotalPersonnelAddress($data->personnel_id);
        $data->total_address = $totalAddressPersonnel;

        /* Get Roles for users has Login ID */
        $roles = $this->profileService->getUserRoles($data->user_id);
        $data->roles = $roles;
    }

    public function getTotalPersonnelAddress($personnelId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " select count(*) as total_address from SM_PERSONNEL_ADDRESS
                    where
                      record_status = 1
                      and personnel_id =  ? ",
            array($personnelId)
        );
        if (count($results) > 0) {
            return $results[0]->total_address;
        }
        return 0;
    }

    public function checkSoftcertIsONLINE($softcertRequestId, $userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SOFTCERT_REQUEST as PSR');
        $query->join('SM_SOFTCERT_DOC as SD', 'PSR.USER_ID', '=', 'SD.USER_ID');
        $query->where('SD.record_status', 1);
        $query->where('PSR.softcert_request_id', $softcertRequestId);
        $query->where('PSR.user_id', $userId);
        $query->whereRaw("trunc(PSR.changed_date) = trunc(SD.changed_date)");
        return $query->count();
    }

    public function getTrustgateDetailInfo($mofNo, $icNo)
    {
        $query = DB::table('softcert_trustgate_mig as stm');
        $query->where('stm.mof_no', $mofNo);
        $query->where('stm.ic_no', $icNo);
        return $query->first();
    }

    public function getListSoftCertRequest($epNo, $userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SOFTCERT_REQUEST as PSR');
        $query->leftJoin('PM_DIGI_CERT as PDC', 'PSR.SOFTCERT_REQUEST_ID', '=', 'PDC.SOFTCERT_REQUEST_ID');
        $query->where('PSR.EP_NO', $epNo);
        $query->where('PSR.USER_ID', $userId);
        $query->select('PSR.SOFTCERT_REQUEST_ID', 'PSR.USER_ID', 'PSR.EP_NO', 'PSR.RECORD_STATUS', 'PSR.CREATED_DATE', 'PSR.CHANGED_DATE', 'PSR.SOFTCERT_PROVIDER');
        $query->addSelect('PSR.IS_FREE', 'PSR.RESPONSE_STATUS', 'PSR.REQUEST_MODE', 'PSR.REMARK', 'PSR.REASON_CODE');
        $query->addSelect('PDC.RECORD_STATUS AS PDC_RECORD_STATUS', 'PDC.CERT_SERIAL_NO', 'PDC.VALID_FROM', 'PDC.VALID_TO', 'PDC.ORN', 'PDC.PRN', 'PDC.CERT_MODE', 'PDC.CERT_ISSUER', 'PDC.CREATED_DATE as PDC_CREATED_DATE', 'PDC.CHANGED_DATE as PDC_CHANGED_DATE');
        $query->orderBy('PSR.CREATED_DATE', 'desc');
        $result = $query->get();
        if (count($result) > 0) {
            return $result;
        }
        return array();
    }

    public function getActivationLink($companyName, $ssmNO, $icno, $email)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_NOTIFY_MESSAGE as P');
        $query->where('P.notify_mode', 'D');
        $query->where('P.notification_id', '399999');
        $query->where('P.EMAIL_LIST', $email);
        $query->where('P.subject_param', 'like', $companyName . '%');
        $query->orderBy('CREATED_DATE', 'desc');
        return $query->first();
    }

    public function getSmApplDetail($applId)
    {
        $query = "select a.* ,
                        (SELECT status_name
                                            FROM pm_status_desc
                                           WHERE status_id = a.status_id
                                             AND language_code = 'en') AS status_name                   

                     from sm_appl a
                     where a.appl_id = ? ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($applId));
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    public function getTrackingDiarySupplierByDocNo($docNo)
    {
        $query = "select d.* ,
                        (SELECT status_name
                                            FROM pm_status_desc
                                           WHERE status_id = d.status_id
                                             AND language_code = 'en') AS status_name,
                        (SELECT usr.user_name
                                FROM pm_user usr
                               WHERE usr.user_id = d.actioned_by) diary_actioned_by                     

                     from pm_tracking_diary d
                     where d.doc_no = ? 
                     order by d.tracking_diary_id desc ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getListAttachmentRejectOrCancel($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_attachment as att', 'appl.appl_id', '=', 'att.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id', 'appl.supplier_id', 'appl.appl_type', 'appl.appl_no');
        $query->addSelect('att.attachment_id', 'att.doc_type', 'att.file_name', 'att.file_desc', 'att.file_path', 'att.created_date as att_created_date');
        return $query->get();
    }

    public function getListRemarksRejectOrCancel($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_remark as rem', 'appl.appl_id', '=', 'rem.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id', 'appl.supplier_id', 'appl.appl_type', 'appl.appl_no');
        $query->addSelect('rem.remark_id', 'rem.doc_type', 'rem.doc_id', 'rem.remark', 'rem.created_date as rem_created_date');
        return $query->get();
    }

    public function getTotalItemsSupplier($supplierID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_SUPPLIER_ITEM as csi');
        $query->where('csi.supplier_id', $supplierID);
        $query->whereIn('csi.item_status', ['A', 'U']);
        $query->where('csi.record_status', 1);
        return $query->count();
    }

    public function getMainSapVendorCode($epNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.record_status', 1);
        $query->whereNull('svc.branch_code');
        return $query->first();
    }

    public function getSupplierMofStatus($supplierId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT ss.ep_no,sa.appl_id,ss.supplier_id,
                    DECODE (ss.is_bumi, 1, 'Bumi', 'Non-Bumi') bumi_status,
                    CASE
                       WHEN sa.reg_status_id IN ('1')
                          THEN 'Not defined'
                       WHEN sa.reg_status_id IN ('2')
                          THEN 'Bumiputera'
                       WHEN sa.reg_status_id IN ('3')
                          THEN 'Registered'
                       WHEN sa.reg_status_id IN ('4')
                          THEN 'Bumi operate from home'
                       WHEN sa.reg_status_id IN ('5')
                          THEN 'Non bumi operate from home'
                       WHEN sa.reg_status_id IN ('6')
                          THEN 'Joint venture bumi'
                       WHEN sa.reg_status_id IN ('7')
                          THEN 'Joint venture non bumi'
                       WHEN sa.reg_status_id IN ('8')
                          THEN 'Foreign company'
                    END AS type
               FROM sm_appl sa, sm_supplier ss
              WHERE sa.appl_id = ss.latest_appl_id AND ss.supplier_id = ? ",
            array($supplierId)
        );
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    public function getInProgressWorkFlowSupplierProcess($supplierID, $applId = null)
    {
        $addWhereApplIdClause = '';
        if ($applId != null) {
            $addWhereApplIdClause = "AND appl.appl_id = '$applId' ";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   wf.is_current, sup.supplier_id, sup.company_name,
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    appl.supporting_doc_mode,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'
                    and ppd.RECORD_STATUS = 1
                    and pp.RECORD_STATUS = 1
                    and pp.PARAMETER_TYPE = 'AT'
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type ,
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    wf.workflow_status_id,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ?
                $addWhereApplIdClause 
                AND appl.IS_ACTIVE_APPL = 1
           ORDER BY wf.created_date DESC, wf.is_current DESC ",
            array($supplierID)
        );

        return $results;
    }

    public function getBasicSupplierInfo($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_COMPANY_BASIC as SCB');
        $query->leftJoin('SM_COMPANY_ADDRESS as SCD', 'SCB.COMPANY_BASIC_ID', '=', 'SCD.COMPANY_BASIC_ID');
        $query->leftJoin('SM_ADDRESS as SA', 'SCD.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as PD', 'SA.DIVISION_ID', '=', 'PD.DIVISION_ID');
        $query->leftJoin('PM_CITY as PCT', 'SA.CITY_ID', '=', 'PCT.CITY_ID');
        $query->leftJoin('PM_DISTRICT as PDR', 'SA.DISTRICT_ID', '=', 'PDR.DISTRICT_ID');
        $query->leftJoin('PM_STATE as PS', 'SA.STATE_ID', '=', 'PS.STATE_ID');
        $query->leftJoin('PM_COUNTRY as PCR', 'SA.COUNTRY_ID', '=', 'PCR.COUNTRY_ID');
        $query->where('SCB.APPL_ID', $applID);
        $query->where('SCB.REV_NO', DB::raw("(select max(rev_no) from SM_COMPANY_BASIC WHERE APPL_ID=SCB.APPL_ID)"));
        $query->select('SCB.COMPANY_BASIC_ID', 'SCB.APPL_ID', 'SCB.COMPANY_NAME');
        $query->addSelect('SCB.IS_WITH_FEDERAL', 'SCB.IS_WITH_STATE', 'SCB.IS_WITH_STATUTORY', 'SCB.IS_WITH_GLC', 'SCB.IS_WITH_OTHERS', 'SCB.RECORD_STATUS');
        $query->addSelect('SCB.PHONE_COUNTRY', 'SCB.PHONE_AREA', 'SCB.PHONE_NO', 'SCB.FAX_COUNTRY', 'SCB.FAX_AREA', 'SCB.FAX_NO');
        $query->addSelect('SCB.IS_REVENUE_SSM_NULL', 'SCB.IS_ROB_EXPIRY_SSM_NULL', 'SCB.IS_AUTHORIZED_CAPITAL_SSM_NULL', 'SCB.COMPANY_SSM_STATUS', 'SCB.ROB_EXPIRY_DATE', 'SCB.BUSINESS_DESC', 'SCB.ANNUAL_REVENUE_SSM', 'SCB.PAID_UP_CAPITAL_SSM', 'SCB.SSM_COMPANY_COUNTRY');
        $query->addSelect('SA.ADDRESS_ID', 'SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE');
        $query->addSelect('PD.DIVISION_NAME', 'PCT.CITY_NAME', 'PDR.DISTRICT_NAME', 'PS.STATE_NAME', 'PCR.COUNTRY_NAME');
        //$query->orderBy('SCB.REV_NO', 'desc');
        return $query->first();
    }

    public function getListApplRejectReason($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_APPL_REJECT_REASON rej');
        $query->join('PM_REASON_DESC as rea', 'rea.reason_id', '=', 'rej.reason_id');
        $query->where('rej.appl_id', $applID);
        $query->where('rej.record_status', 1);
        $query->where('rea.language_code', 'en');
        $query->select('rea.reason_desc', 'rea.reason_id');
        $query->addSelect('rej.appl_id', 'rej.appl_reject_reason_id', 'rej.changed_date', 'rej.created_date');
        return $query->get();
    }

    public function getListApplSectionReview($applID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_APPL_SECTION_REVIEW');
        $query->where('appl_id', $applID);
        $query->where('record_status', 1);
        $query->select('appl_section_review_id', 'role_type', 'created_date', 'changed_date', 'remark_date', 'basic_remark', 'staff_remark', 'office_facility_remark', 'trns_network_remark', 'branch_remark', 'associate_remark', 'cert_remark', 'experience_remark', 'capital_remark', 'shareholder_remark', 'equity_comp_remark', 'personnel_remark', 'bank_remark', 'category_remark', 'factory_remark', 'manufacturing_remark', 'machinery_remark', 'sitevisit_remark', 'recommendation', 'remark_to_approver', 'license_remark', 'assoc_body_remark', 'sitevisit_verified_remark', 'supplier_system_remark', 'recomm_reg_status_id');
        return $query->get();
    }

    public function getHistoryApplCsView($supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    SELECT
                    a.supplier_id,
                    a.appl_id,
                    a.appl_no,
                    a.appl_type,
                    a.appl_submit_date,
                    a.changed_date ,
                    a.status_id,
                    b.status_name,
                CASE
                        WHEN a.supporting_doc_mode = 'H' THEN 'Offline'
                        WHEN a.supporting_doc_mode = 'S' THEN 'Online'
                        ELSE ''
                    END AS supporting_doc_mode,
                    a.appl_category,
                CASE
                        WHEN a.is_resubmit = 1 THEN 'Yes'
                        ELSE 'No'
                    END AS is_resubmit
                FROM
                    sm_appl a,
                    pm_status_desc b
                WHERE
                    a.status_id = b.status_id
                    AND language_code = 'en'
                    AND supplier_id = ?
                    AND a.appl_no IS NOT NULL 
                    AND (b.status_name = 'Registered' OR b.status_name LIKE '%ejec%' ) 
                ORDER BY appl_id desc ", array($supplierId));
        return $query;
    }

    public function getSMSupplierUsersDetailsByMofNOorEpNo($mofno, $epNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->join('SM_APPL as A', 'A.APPL_ID', '=', 'P.APPL_ID');
        if ($mofno != null) {
            $query->join('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        if ($epNo != null && $mofno == null) {
            $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        //$query->whereNotIn('P.RECORD_STATUS', [9]);
        if ($mofno != null) {
            $query->where('MA.MOF_NO', $mofno);
        }
        if ($epNo != null) {
            $query->where('S.EP_NO', $epNo);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));

        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.PREFERRED_LANGUAGE', 'U.IS_NOTIFY_BY_SMS');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID', 'P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE', 'P.IDENTITY_RESIDENT_STATUS');
        $query->addSelect(DB::raw("(select race_name from pm_race where race_id=p.race_id) as race_name"));
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID', 'S.LOCAL_AUTHORITY_ID');
        $query->addSelect('A.APPL_NO', 'A.SUPPLIER_TYPE', 'A.APPL_TYPE', 'A.SUPPORTING_DOC_MODE');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        if ($epNo != null && $mofno == null) {
            $query->orderBy('MA.EXP_DATE', 'desc');
            $query->orderBy('P.EP_ROLE');
        } else {
            $query->orderBy('P.EP_ROLE');
        }
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->get();
    }

    public function getEpNoSmSupplier($search)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
            $query->orWhere('A.REG_NO', strtoupper($search))
                ->orWhere('A.COMPANY_NAME', strtoupper($search))
                ->orWhere('A.LATEST_APPL_ID', intval($search));
        });
        $query->orderBy('A.RECORD_STATUS', 'asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj) {
                if ($obj->record_status == 1) {
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }

    public function getEpNoSmSupplierByApplNo($search)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->join('SM_APPL as B', 'A.SUPPLIER_ID', '=', 'B.SUPPLIER_ID');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
            $query->orWhere('B.APPL_NO', strtoupper($search))
                ->orWhere('B.APPL_ID', intval($search));
        });
        $query->orderBy('A.RECORD_STATUS', 'asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj) {
                if ($obj->record_status == 1) {
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }

    public function checkSupplierMofExpiredNotActivate($supplierId)
    {
        $query = "
            select s.supplier_id,s.company_name,s.ep_no,
                a.appl_no,a.supplier_type,a.created_date,a.changed_date ,
                  m.mof_no,m.exp_date
                from sm_supplier s , sm_appl a, sm_mof_account m
                where s.latest_appl_id = a.appl_id
                and s.supplier_id = m.supplier_id
                and a.status_id = 20199
                and a.appl_type in ('R','N')
                and s.supplier_id = ?
                -- and to_char(a.changed_date,'YYYY') = to_char(sysdate,'YYYY')
                and a.changed_date in (select max(d.changed_date) from sm_appl d where d.supplier_id = s.supplier_id and d.appl_type in ('R','N') and d.status_id = 20199) 
                and m.record_status = 9
                and not exists (select 1 from sm_mof_account where supplier_id = s.supplier_id and record_status = 1)
                and exists (
                select p.payment_id,p.receipt_no,b.bill_type,b.bill_no from py_payment p, py_bill b
                where p.bill_id = b.bill_id
                and p.receipt_no is not null
                and b.bill_type = 'R' 
                and b.bill_no = a.appl_no 
                and b.org_profile_id = s.supplier_id) 
                ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supplierId));
        return $results;
    }

    public function getDetailSupplierDisciplinaryAction($supplierId)
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM SM_DISCIPLINARY_ACTION  a
            WHERE a.SUPPLIER_ID = ? 
            AND a.created_date = (SELECT max(created_date) FROM SM_DISCIPLINARY_ACTION x WHERE x.supplier_id = a.supplier_id )
            ", array($supplierId));
        if (count($list) > 0) {
            return $list[0];
        }
        return null;
    }

    public function getWorkFlowSupplierProcess($supplierID, $applID)
    {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   wf.is_current, sup.supplier_id, sup.company_name,
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    appl.record_status,
                    appl.supporting_doc_mode,
                    appl.appl_no,
                    appl.appl_category,
                    appl.is_resubmit,
                    appl.is_active_appl,
                    appl.original_appl_id,
                    appl.supporting_doc_mode,
                    appl.is_appl_valid_with_ssm,
                    appl.reg_status_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'
                    and ppd.RECORD_STATUS = 1
                    and pp.RECORD_STATUS = 1
                    and pp.PARAMETER_TYPE = 'AT'
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type ,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    wf.workflow_status_id,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id(+)
                AND appl.appl_id = wf.doc_id
                AND sup.SUPPLIER_ID = ?
                AND appl.appl_id = ?
           ORDER BY wf.created_date DESC, wf.is_current DESC ",
            array($supplierID, $applID)
        );

        return $results;
    }

    public function getSupplierMofVirtCert($supplierId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT  smc.mof_cert_id, DECODE (smc.is_bumi_cert, 1, 'Bumi Cert', 'Mof Cert')  cert_type, smc.cert_serial_no, smc.eff_date, smc.exp_date,
                         smc.record_status,smc.appl_id
                    FROM sm_mof_account sma, sm_mof_cert smc
                   WHERE sma.MOF_ACCOUNT_ID = smc.MOF_ACCOUNT_ID
                     AND sma.RECORD_STATUS = 1
                     AND smc.RECORD_STATUS = 1
                     AND sma.supplier_id = ? ",
            array($supplierId)
        );
        if (count($results) > 0) {
            return $results;
        }
        return null;
    }

    public function getPaymentSuppliers($supplierID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select distinct ss.supplier_id,ss.company_name,ss.ep_no,
                    pb.bill_no,pb.bill_type,pb.bill_date,pb.bill_ref_id,pb.payment_due_date,pb.bill_amt,
                    pp.payment_date,pp.payment_id,pp.bill_id,
                    CONCAT(CONCAT((select code_desc from pm_parameter_desc where parameter_id = pp.payment_mode_id and language_code = 'ms'), ' - '), pp.payment_mode_id) as payment_mode,
                    pp.payment_amt,
                    CASE 
                    	WHEN  pp.RECEIPT_FILE_NAME  IS NULL AND pb.bill_type = 'S' THEN 'SM_TAX_INVOICE.jsp'
                    	WHEN  pp.RECEIPT_FILE_NAME  IS NULL AND pb.bill_type IN ('P','R') THEN 'sm_payment_receipt.jsp'
                    	ELSE pp.RECEIPT_FILE_NAME
                    END AS receipt_filename,
                    CONCAT(CONCAT((select status_name from PM_STATUS_DESC where status_id = pp.status_id and language_code = 'ms'), ' - '), pp.status_id) as status,
                    pp.receipt_no , ppo.payment_gateway,
                    pbd.bill_dtl_ref_id as ref_id,
                    (select name||' - '|| identification_no from sm_personnel where personnel_id = pbd.bill_dtl_ref_id) as personnel,
                    spp.pending_process_id,spp.attempt as pending_process_attempt,spp.err_msg as pending_process_err_msg,spp.record_status  as pending_process_status
                  from sm_supplier ss , py_bill pb ,
                    py_payment pp ,
                    py_payment_dtl  ppd ,
                    py_payment_order ppo,
                    PY_BILL_DTL pbd,
                    SM_PENDING_PROCESS spp
                    where
                    ss.supplier_id = pb.ORG_PROFILE_ID
                    and pb.BILL_ID = pp.BILL_ID (+)
                    and pp.PAYMENT_ID = ppd.PAYMENT_ID (+)
                    and ppd.BILL_DTL_ID = pbd.BILL_DTL_ID
                    and pb.BILL_ID = pbd.BILL_ID (+)
                    and pp.payment_id = spp.payment_id (+)
                    AND pp.PAYMENT_ID = ppo.PAYMENT_ID (+)
                    and ss.SUPPLIER_ID = ?
                    and pp.payment_id is not null
                  ORDER BY pp.payment_date desc ",
            array($supplierID)
        );
        return $results;
    }

    public function getFacilityInfo($applId)
    {

        $query = "SELECT scb.paid_up_capital,
       scb.paid_up_capital_ssm,
          DECODE (fac.facility_type,
                  'H', 'Home Office',
                  'B', 'Business Office',
                  'F', 'Factory/Kilang',
                  'W', 'Workshop/Bengkel',
                  'D', 'Dockyard/Limbungan',
                  'Unknown'
                 )
       || ' : '
       || fac.owner_name
       || ' - Ownership:  '
       || DECODE (fac.ownership_type, 'R', 'Rented', 'O', 'Owned', 'Unknown') as facility_type
  FROM sm_facility fac, sm_company_basic scb
 WHERE fac.appl_id = scb.appl_id
   AND fac.appl_id = $applId
   AND scb.record_status = 1
   AND fac.record_status = 1  
   AND fac.facility_type in ('H','B')";

        $result = DB::connection('oracle_nextgen_rpt')->select($query);

        return $result;
    }

    public function getListSupplierBranch($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_BRANCH as sb');
        $query->join('SM_ADDRESS as sa', 'sb.ADDRESS_ID', '=', 'sa.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as pd', 'sa.DIVISION_ID', '=', 'pd.DIVISION_ID');
        $query->leftJoin('PM_CITY as pct', 'sa.CITY_ID', '=', 'pct.CITY_ID');
        $query->leftJoin('PM_DISTRICT as pdr', 'sa.DISTRICT_ID', '=', 'pdr.DISTRICT_ID');
        $query->leftJoin('PM_STATE as ps', 'sa.STATE_ID', '=', 'ps.STATE_ID');
        $query->leftJoin('PM_COUNTRY as pcr', 'sa.COUNTRY_ID', '=', 'pcr.COUNTRY_ID');
        $query->leftJoin('SM_GST_SUPPLIER as gst', function ($join) {
            $join->on('gst.BRANCH_CODE', '=', 'sb.BRANCH_CODE')
                ->on('gst.IS_CURRENT', DB::raw('1'));
        });
        $query->where('sb.APPL_ID', $applID);
        $query->where('sb.REV_NO', DB::raw("(select max(rev_no) from SM_SUPPLIER_BRANCH WHERE APPL_ID=sb.APPL_ID  and branch_code = sb.branch_code  )"));
        $query->select('sb.*');
        $query->addSelect('sa.ADDRESS_ID', 'sa.ADDRESS_1', 'sa.ADDRESS_2', 'sa.ADDRESS_3', 'sa.POSTCODE');
        $query->addSelect('pd.DIVISION_NAME', 'pct.CITY_NAME', 'pdr.DISTRICT_NAME', 'ps.STATE_NAME', 'pcr.COUNTRY_NAME');
        $query->addSelect('gst.GST_REG_NO', 'gst.GST_EFF_DATE', 'gst.GST_DECLARED_DATE');
        return $query->get();
    }

    public function getMainSapVendorCodeByBranchCode($epNo, $branchCode)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.branch_code', $branchCode);
        $query->where('svc.record_status', 1);
        return $query->first();
    }

    public function getListSupplierBank($applID)
    {
        return $query = DB::connection('oracle_nextgen_rpt')->select("SELECT fo.FIN_ORG_NAME,
            b.supplier_bank_id,
            b.appl_id,b.account_no,
            CASE 
                WHEN b.account_no LIKE '% %' OR b.account_no LIKE '%-%' THEN 1
                ELSE 0
            END AS is_invalid_bank_no,
            b.account_purpose,b.is_default_account,b.IS_FOR_HQ,b.BANK_BRANCH,b.changed_date,
            sb.branch_name,sb.BRANCH_CODE,sb.APPL_ID, fo.record_status as bank_status 
            FROM SM_SUPPLIER_BANK b
            INNER JOIN PM_FINANCIAL_ORG fo on fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
            LEFT JOIN SM_SUPPLIER_BRANCH_BANK bb on bb.SUPPLIER_BANK_ID = b.SUPPLIER_BANK_ID and bb.record_status = 1
            LEFT JOIN SM_SUPPLIER_BRANCH sb on sb.SUPPLIER_BRANCH_ID  = bb.SUPPLIER_BRANCH_ID  and  sb.record_status = 1
            WHERE
            b.appl_id  = ?
            AND b.REV_NO = (SELECT max(x.rev_no) FROM  SM_SUPPLIER_BANK x WHERE x.appl_id = b.appl_id )
            ", array($applID));
    }

    public function getHqGstInfo($supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_GST_SUPPLIER as GST');
        $query->where('GST.SUPPLIER_ID', $supplierId);
        $query->where('GST.BRANCH_CODE', null);
        $query->where('GST.IS_CURRENT', 1);
        return $query->first();
    }

    public function getListSupplierCategoryCode($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_CATEGORY as ssc');
        $query->leftJoin('PM_CATEGORY_L1 as l1', 'ssc.CATEGORY_L1_ID', '=', 'l1.CATEGORY_L1_ID');
        $query->leftJoin('PM_CATEGORY_L2 as l2', 'ssc.CATEGORY_L2_ID', '=', 'l2.CATEGORY_L2_ID');
        $query->leftJoin('PM_CATEGORY_L3 as l3', 'ssc.CATEGORY_L3_ID', '=', 'l3.CATEGORY_L3_ID');
        $query->where('ssc.APPL_ID', $applID);
        $query->whereIn('ssc.RECORD_STATUS', [1, 8]);
        //$query->whereNotNull('ssc.APPROVED_DATE');
        $query->select('ssc.*');
        $query->addSelect('l1.CATEGORY_L1_CODE', 'l2.CATEGORY_L2_CODE', 'l3.CATEGORY_L3_CODE');
        $query->addSelect(DB::raw(" l1.CATEGORY_L1_CODE||l2.CATEGORY_L2_CODE||l3.CATEGORY_L3_CODE as category_code"));
        return $query->get();
    }

    public function getEpNoBySapVendorCode($sapVendorCode)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as A');
        $query->where('A.SAP_VENDOR_CODE', $sapVendorCode);
        $query->where('A.RECORD_STATUS', 1);
        $result = $query->first();
        if ($result) {
            return $result->ep_no;
        }
        return null;
    }

    public function getLatestGpkiSuccessSigning($userId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select upper(to_char(max(pds.created_date), 'dd-Mon-yyyy hh:mi:ss AM')) as latest_signing
                from PM_DIGI_SIGN pds 
                where pds.created_by = ?
                and pds.GPKI_SIGNED_DATA IS NOT NULL",
            array($userId)
        );

        return $results;
    }

    public function getItemsSupplier($supplierID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_SUPPLIER_ITEM as csi');
        $query->join('CM_ITEM as ci', 'ci.item_id', '=', 'csi.ITEM_ID');
        $query->leftJoin('PM_UOM as pu', 'csi.UOM_ID', '=', 'pu.UOM_ID');
        $query->where('csi.supplier_id', $supplierID);
        $query->whereIn('csi.item_status', ['A', 'U']);
        $query->where('csi.record_status', 1);
        $query->select('csi.supp_item_id', 'csi.supplier_id', 'csi.item_id', 'csi.item_status', 'csi.uom_id', 'csi.created_date', 'csi.changed_date');
        $query->addSelect('ci.extension_code', 'ci.item_name');
        $query->addSelect('pu.uom_name');
        return $query->get();
    }

    public function getHistoryApplId($suppid)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select paid_up_capital,a.supplier_id, a.appl_id,a.appl_no, a.appl_type, a.appl_submit_date, a.changed_date , b.status_name, 
                        case when a.supporting_doc_mode = 'H' then 'Offline' when a.supporting_doc_mode = 'S' then 'Online' else '' end as supporting_doc_mode, a.appl_category,
                        case when a.is_resubmit = 1 then 'Yes' else 'No' end as is_resubmit, a.original_appl_id,case when a.is_appl_valid_with_ssm=1 then 'Yes' else 'No' end as is_appl_valid_with_ssm
                        from sm_appl a, pm_status_desc b  , SM_COMPANY_BASIC scb 
                        where a.status_id = b.status_id
                        AND a.APPL_ID = scb.APPL_ID
                        and language_code = 'en'
                        and supplier_id = ? ", array($suppid));
        return $query;
    }
}