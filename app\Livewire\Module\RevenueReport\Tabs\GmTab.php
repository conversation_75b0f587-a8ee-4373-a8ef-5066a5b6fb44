<?php

namespace App\Livewire\Module\RevenueReport\Tabs;

use App\Livewire\Forms\RevenueReport\DailySummarySearchForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;
use Log;

class GmTab extends Component {

    public DailySummarySearchForm $dailySummarySearchForm;

    // Public properties to store data
    public $listdataPv = []; 
    public $listdataTr = []; 

    public function boot(RevenueService $RevenueService)
    {
        $this->revenueService = $RevenueService;
    }

    public function mount()
    { 
        try{
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $dateTemp = Carbon::parse($yesterday)->format('d-m-Y');
            $listPV = $this->revenueService->cmsListSummaryPVDailyByDate($dateTemp);
            $listTR = $this->revenueService->cmsListSummaryTRDailyByDate($dateTemp);
            $this->listdataPv = $listPV;
            $this->listdataTr = $listTR;
        } catch (\Exception $e) {
            $this->listdataPv = [];
            $this->listdataTr = [];
        } finally {
        }
        
    }

    public function render()
    {
        return view('livewire.revenue-report.tabs.gm-tab', [
            'listdatapv' => $this->listdataPv,
            'listdatatr' => $this->listdataTr
        ]);
    }
}