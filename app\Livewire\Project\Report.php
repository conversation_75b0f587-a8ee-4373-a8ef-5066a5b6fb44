<?php

namespace App\Livewire\Project;

use Livewire\Component;

class Report extends Component
{
    public $activeTab;

    public function mount()
    {
        $this->activeTab = request()->query('tab');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        return redirect()->route('report', ['tab' => $tab]);
    }

    public function render()
    {
        return view('livewire.project.report.index');
    }
}