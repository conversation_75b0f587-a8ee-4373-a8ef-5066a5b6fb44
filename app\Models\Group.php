<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Group extends Model
{
    use SoftDeletes, HasFactory;

    protected $fillable = [
        'name',
        'description'
    ];

    /**
     * The users that belong to the group.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'users_groups', 'group_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * The roles that belong to the group.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'groups_roles', 'group_id', 'role_id')
            ->withTimestamps();
    }
}