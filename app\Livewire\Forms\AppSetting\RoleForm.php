<?php

namespace App\Livewire\Forms\AppSetting;

use Livewire\Form;
use App\Models\Role;

class RoleForm extends Form
{
    public $roleId;
    public $roleCode = '';
    public $roleName = '';
    public $roleDescription = '';

    public function rules()
    {
        return [
            'roleCode' => 'required|unique:roles,role_code,' . $this->roleId,
            'roleName' => 'required',
        ];
    }

    public function save()
    {
        $roleData = [
            'role_code' => $this->roleCode,
            'role_name' => $this->roleName,
            'description' => $this->roleDescription,
        ];

        if ($this->roleId) {
            $role = Role::find($this->roleId);
            $role->update($roleData);
        } else {
            $role = Role::create($roleData);
        }

        return $role;
    }

    public function edit(Role $role)
    {
        $this->roleId = $role->id;
        $this->roleCode = $role->role_code;
        $this->roleName = $role->role_name;
        $this->roleDescription = $role->description;
    }

    public function resetForm()
    {
        $this->roleId = null;
        $this->roleCode = '';
        $this->roleName = '';
        $this->roleDescription = '';
    }
}