<?php

namespace App\Livewire\AppSetting;

use Auth;
use Livewire\Component;
use App\Models\Role;
use App\Models\Permission;
use App\Livewire\Forms\AppSetting\RoleForm;

class Roles extends Component
{
    public RoleForm $roleForm;
    public $roleIdToDelete = null;
    public $allRoutes = [];
    public $roleFormPermissions = [];

    // Modal state properties
    public $isModalOpen = false;
    public $isDeleteModalOpen = false;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    private function authorizeAction(string $action): void
    {
        if (!$this->userCan($action)) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $this->refreshRoutesList();
    }

    protected function refreshRoutesList()
    {
        // Get all unique route names from permissions
        $routeNames = Permission::select('route_name')->distinct()->get();

        $this->allRoutes = [];

        // For each route, determine which actions are available
        foreach ($routeNames as $routeItem) {
            $routeName = $routeItem->route_name;

            // Get all actions that exist for this route
            $availableActions = Permission::where('route_name', $routeName)
                ->pluck('action')
                ->toArray();

            $this->allRoutes[] = [
                'route_name' => $routeName,
                'available_actions' => $availableActions
            ];
        }
    }

    public function openAddRoleModal()
    {
        $this->authorizeAction('create');
        $this->resetForms();
        $this->isModalOpen = true;
        $this->dispatch('open-role-modal');
    }

    public function openEditRoleModal($id)
    {
        $this->authorizeAction('update');
        $role = Role::with('permissions')->find($id);
        $this->roleForm->edit($role);

        foreach ($this->allRoutes as $route) {
            $routeName = $route['route_name'];

            foreach (['create', 'read', 'update', 'delete'] as $action) {
                $hasPermission = $role->permissions
                    ->where('route_name', $routeName)
                    ->where('action', $action)
                    ->count() > 0;

                $this->roleFormPermissions[$routeName][$action] = $hasPermission ? 1 : 0;
            }
        }

        $this->isModalOpen = true;
        $this->dispatch('open-role-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->resetForms();
    }

    public function openDeleteRoleModal($id)
    {
        $this->authorizeAction('delete');
        $this->roleIdToDelete = $id;
        $this->isDeleteModalOpen = true;
        $this->dispatch('open-delete-role-modal');
    }

    public function closeDeleteModal()
    {
        $this->isDeleteModalOpen = false;
        $this->roleIdToDelete = null;
    }

    public function resetForms()
    {
        $this->roleForm->resetForm();
        $this->roleFormPermissions = [];
    }

    public function saveRole()
    {
        $action = $this->roleForm->roleId ? 'update' : 'create';
        $this->authorizeAction($action);

        $this->roleForm->validate();

        $role = $this->roleForm->save();

        // Save permissions
        \DB::beginTransaction();
        try {
            $isNewRole = !$this->roleForm->roleId;

            if (!$isNewRole) {
                // Get affected users for existing role
                $affectedUserIds = $role->users->pluck('id')->toArray();

                // Also include users who are in groups that have this role
                $groupUserIds = \DB::table('users_groups')
                    ->whereIn('group_id', $role->groups->pluck('id'))
                    ->pluck('user_id')
                    ->toArray();

                $affectedUserIds = array_unique(array_merge($affectedUserIds, $groupUserIds));
            }

            // Detach all existing permissions
            $role->permissions()->detach();

            // Attach the selected permissions
            foreach ($this->roleFormPermissions as $routeName => $actions) {
                foreach ($actions as $action => $value) {
                    if ($value) {
                        // Find or create the permission
                        $permission = Permission::firstOrCreate([
                            'route_name' => $routeName,
                            'action' => $action,
                        ], [
                            'name' => ucfirst($action) . ' ' . $routeName,
                            'description' => 'Permission to ' . $action . ' ' . $routeName,
                        ]);

                        // Attach permission to role
                        $role->permissions()->attach($permission->id);
                    }
                }
            }

            // Clear caches for affected users if updating an existing role
            if (!$isNewRole) {
                foreach ($affectedUserIds as $userId) {
                    $cacheKey = "user_{$userId}_role_{$role->role_code}";
                    cache()->forget($cacheKey);
                }
            }

            \DB::commit();
        } catch (\Exception $e) {
            \DB::rollBack();
            session()->flash('error', 'Failed to update permissions: ' . $e->getMessage());
            return;
        }

        $this->resetForms();
        $this->isModalOpen = false;
        session()->flash('message', 'Role and permissions saved successfully!');
        $this->dispatch('role-saved');
    }

    public function executeDeleteRole()
    {
        $this->authorizeAction('delete');

        if ($this->roleIdToDelete) {
            $role = Role::with(['users', 'groups'])->find($this->roleIdToDelete);

            if ($role) {
                // Get all affected users (direct and via groups)
                $directUserIds = $role->users->pluck('id')->toArray();

                $groupUserIds = \DB::table('users_groups')
                    ->whereIn('group_id', $role->groups->pluck('id'))
                    ->pluck('user_id')
                    ->toArray();

                $affectedUserIds = array_unique(array_merge($directUserIds, $groupUserIds));

                // Clear role cache for all affected users
                foreach ($affectedUserIds as $userId) {
                    $cacheKey = "user_{$userId}_role_{$role->role_code}";
                    cache()->forget($cacheKey);
                }

                Role::destroy($this->roleIdToDelete);
            }

            $this->roleIdToDelete = null;
            $this->isDeleteModalOpen = false;
            session()->flash('message', 'Role deleted successfully!');
            $this->dispatch('role-deleted');
        }
    }

    // Keep these methods for backward compatibility
    public function editRole($id)
    {
        $this->openEditRoleModal($id);
    }

    public function confirmDeleteRole($id)
    {
        $this->openDeleteRoleModal($id);
    }

    public function render()
    {
        return view('livewire.app-setting.roles', [
            'roles' => Role::all(),
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}