<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ep_login_history', function (Blueprint $table) {
            $table->id();
            $table->string('user_id', 50);
            $table->string('username', 100);
            $table->string('fullname', 150)->nullable();
            $table->string('email', 100)->nullable();
            $table->text('groups')->nullable();
            $table->dateTime('last_login');

            $table->unique(['user_id', 'last_login']);
            $table->unique(['username', 'last_login']);
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ep_login_history');
    }
};
