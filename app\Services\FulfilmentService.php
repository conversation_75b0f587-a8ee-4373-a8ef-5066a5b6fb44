<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class FulfilmentService
{
    public function getListDocNoFulfillmentSQPOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "  SELECT DISTINCT q.QUOTE_ID, q.QUOTE_NO, 
                    rn.REQUEST_NOTE_ID, rn.REQUEST_NOTE_NO, rn.SOURCE_METHOD, 
                    rnd.PURCHASE_REQUEST_ID, rnd.FULFILMENT_TYPE_ID,
                    fr.FULFILMENT_REQ_ID, fr.DOC_TYPE as FR_DOC_TYPE, fr.DOC_NO as FR_DOC_NO,
                    fo.DOC_NO as FO_DOC_NO,fo.DOC_TYPE as FO_DOC_TYPE,fo.FULFILMENT_ORDER_ID 
                  FROM SC_QUOTE q, SC_REQUEST_NOTE rn, SC_REQUEST_NOTE_DTL rnd, FL_FULFILMENT_REQUEST fr, FL_FULFILMENT_ORDER fo  
                  WHERE 
                    q.QUOTE_ID = rn.QUOTE_ID (+)  
                    AND rn.REQUEST_NOTE_ID = rnd.REQUEST_NOTE_ID(+) 
                    AND rnd.PURCHASE_REQUEST_ID = fr.PURCHASE_REQUEST_ID(+)
                    AND fr.FULFILMENT_REQ_ID = fo.FULFILMENT_REQ_ID(+) 
                ";
        if ($type == 'SQ') {
            $query = $query . "
                    AND q.QUOTE_NO = ?
                    ";
        } else if ($type == 'RN') {
            $query = $query . "
                    AND rn.REQUEST_NOTE_NO = ?
                    ";
        } else if ($type == 'PR') {
            $query = $query . "
                    AND fr.DOC_NO = ?
                    ";
        } else if ($type == 'PO') {
            $query = $query . "
                    AND fo.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getListDocNoFulfillmentSQCOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "  SELECT DISTINCT Q.QUOTE_ID, Q.QUOTE_NO, 
                        RN.REQUEST_NOTE_ID, RN.REQUEST_NOTE_NO, RN.SOURCE_METHOD, 
                        RND.FULFILMENT_TYPE_ID,RND.PURCHASE_REQUEST_ID, 
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_QUOTE Q 
                                  LEFT JOIN SC_REQUEST_NOTE RN ON Q.QUOTE_ID = RN.QUOTE_ID
                                  LEFT JOIN SC_REQUEST_NOTE_DTL RND ON RN.REQUEST_NOTE_ID = RND.REQUEST_NOTE_ID
                                  LEFT JOIN SC_LOI_LOA LOI ON RND.PURCHASE_REQUEST_ID = LOI.DOC_ID
                                  LEFT JOIN SC_LOA LOA ON LOI.LOI_LOA_ID = LOA.LOI_LOA_ID
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID
                            WHERE 
                ";
        if ($type == 'SQ') {
            $query = $query . "
                     Q.QUOTE_NO = ?
                    ";
        } else if ($type == 'RN') {
            $query = $query . "
                     RN.REQUEST_NOTE_NO = ?
                    ";
        } else if ($type == 'LA') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'CT') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getListDocNoFulfillmentQTCOTracking($docNo)
    {

        $type = substr($docNo, 0, 1);
        $type2 = substr($docNo, 0, 2);


        $query = " SELECT DISTINCT 
                        QT.QT_NO AS QT_DOC_NO ,  
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                    FROM SC_QT QT  
                    LEFT JOIN  SC_LOI_LOA LOI ON LOI.DOC_ID  = QT.QT_ID 
                    LEFT JOIN SC_LOA LOA ON  LOA.LOI_LOA_ID = LOI.LOI_LOA_ID 
                    LEFT JOIN CT_CONTRACT CT ON CT.LOA_ID = LOA.LOA_ID  
                    LEFT JOIN FL_FULFILMENT_REQUEST FR ON  FR.CONTRACT_ID = CT.CONTRACT_ID 
                    LEFT JOIN FL_FULFILMENT_ORDER FO ON FO.FULFILMENT_REQ_ID = FR.FULFILMENT_REQ_ID  
                    WHERE 
                ";
        if ($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'C' || $type == 'Z' || $type == 'M') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type2 == 'DL' || $type2 == 'QT' || $type2 == 'QM') {
            $query = $query . "
                     QT.QT_NO = ?
                    ";
        } else if ($type2 == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type2 == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getListDocNoFulfillmentPRCRPOCOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "   SELECT DISTINCT 
                            FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                            FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                           FROM FL_FULFILMENT_REQUEST FR 
                      LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID 
                      WHERE 
                ";

        if ($type == 'CR' || $type == 'PR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type == 'PO' || $type == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getListDocNoFulfillmentLOACTCRCOTracking($docNo)
    {

        $type = substr($docNo, 0, 1);
        $type2 = substr($docNo, 0, 2);
        $query = "    SELECT DISTINCT 
                        LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , FR.CREATED_DATE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_LOA LOA 
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID 
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID 
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID  
                        WHERE 
                ";

        if ($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'C' || $type == 'Z' || $type == 'M') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type2 == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type2 == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        //dd($results);
        return $results;
    }

    public function getListDocNoMarketResearchTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "   SELECT DISTINCT 
                            A.APPL_NO AS AP_DOC_NO 
                           FROM PM_ACCESS_APPL A 
                      WHERE 
                       A.APPL_NO = ? 
                ";

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getGroupIdTrackingDiary($docNo)
    {
        $objResult = DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY')
            ->where('DOC_NO', $docNo)
            ->whereNotNull('GROUP_ID')
            ->first();
        return $objResult;
    }

    public function getListTrackingDiary($data)
    {

        $collectResult = collect();
        foreach ($data["list_doc_no"] as $docNo) {
            $listDataTrcDiary = $this->getListTrackingDiaryByDocNo($docNo);
            foreach ($listDataTrcDiary as $res) {
                $collectResult->push($res);
            }
        }

        foreach ($data["list_group_id"] as $groupId) {
            $listTrcDiary = $this->getListTrackingDiaryByGroupId($groupId);
            foreach ($listTrcDiary as $res) {
                $collectResult->push($res);
            }
        }

        return $collectResult->unique();
    }

    public function getListTrackingDiaryByDocNo($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('td.DOC_NO', $docNo)
            ->select('td.*', 'sd.STATUS_NAME')
            ->get();
    }

    public function getListTrackingDiaryByGroupId($groupId)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('td.GROUP_ID', $groupId)
            ->select('td.*', 'sd.STATUS_NAME')
            ->get();
    }

    public function getSupplierInfoByDocNo($docNo)
    {
        $type = substr($docNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');
        $query->join('SM_SUPPLIER as C', 'C.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as D', 'D.SUPPLIER_ID', '=', 'C.SUPPLIER_ID');
        $query->leftJoin('PM_USER as E', 'E.USER_ID', '=', 'A.CREATED_BY');
        $query->leftJoin('PM_USER as F', 'F.USER_ID', '=', 'A.CHANGED_BY');
        if ($type == 'PR' || $type == 'CR') {
            $query->where('A.DOC_NO', $docNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('B.DOC_NO', $docNo);
        }
        $query->select('A.DOC_NO AS FR_DOC_NO', 'A.DOC_TYPE AS FR_DOC_TYPE', 'A.AG_OFFICE_NAME', 'B.DOC_NO AS FO_DOC_NO', 'B.DOC_TYPE AS FO_DOC_TYPE', 'B.SAP_ORDER_NO');
        $query->addSelect('C.*');
        $query->addSelect('D.MOF_NO');
        $query->addSelect('E.USER_ID AS CREATED_USER_ID', 'E.LOGIN_ID AS CREATED_LOGIN_ID', 'F.USER_ID AS CHANGED_USER_ID', 'F.LOGIN_ID AS CHANGED_LOGIN_ID');
        return $query->first();
    }

    public function getPtjInfoByDocNo($docNo, $ptjField)
    {
        $type = substr($docNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');

        if ($ptjField == 'PREPARED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.PREPARED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'ISSUED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.ISSUED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'CREATED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.CREATED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'CHARGE_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.CHARGE_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else {
            $query->join('PM_ORG_PROFILE as C', 'A.CREATED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        }

        $query->join('PM_ORG_VALIDITY as D', 'C.ORG_PROFILE_ID', '=', 'D.ORG_PROFILE_ID');

        $query->where('C.RECORD_STATUS', 1);
        $query->where('D.RECORD_STATUS', 1);
        if ($type == 'PR' || $type == 'CR') {
            $query->where('A.DOC_NO', $docNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('B.DOC_NO', $docNo);
        }
        $query->select('D.*');
        $query->addSelect('ORG_TYPE_ID', 'PARENT_ORG_PROFILE_ID');
        return $query->first();
    }

    public function getListPidByPdNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE , A.PID_NO AS DOC_NO,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY, 
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_PID  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PID_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?  
                    AND A.PID_NO  =  ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PD', $docNo)
        );
        return $results;
    }

    public function getListPurchaseInquiryByPiNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE ,
                    A.PURCHASE_INQUIRY_NO AS DOC_NO,A.DATE_TO_RESPONSE, 
                    A.TITLE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_PURCHASE_INQUIRY  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PURCHASE_INQUIRY_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PURCHASE_INQUIRY_NO = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PI', $docNo)
        );
        return $results;
    }

    public function getListSimpleQuoteBySqNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.QUOTE_NO as DOC_NO ,A.IS_PANEL,  A.END_DATE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_QUOTE  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.QUOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE =?
                    AND B.DOC_TYPE = ?
                    AND A.QUOTE_NO = ?
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'SQ', $docNo)
        );
        return $results;
    }

    public function getListRequestNoteByRnNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.REQUEST_NOTE_NO AS DOC_NO,A.SOURCE_METHOD,A.APPROVER_ID,A.USER_GROUP_ID, A.CHANGED_BY RN_CHANGE,A.CREATED_BY RN_CREATED ,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY ,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_REQUEST_NOTE A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.REQUEST_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ?
                    AND A.REQUEST_NOTE_NO = ?
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'RN', $docNo)
        );
        return $results;
    }

    public function getListFulfilmenRequestByPrCr($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    TO_CHAR (B.CHANGED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CHANGED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,(SELECT DOC_NO FROM FL_FULFILMENT_ORDER O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_CO,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.CREATED_BY, A.CHANGED_BY,A.APPROVER_ID, A.CONTRACT_ID , A.AG_APPROVED_DATE , A.CREATED_ORG_PROFILE_ID,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_REQUEST A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_REQ_ID = B.DOC_ID (+)
                    AND B.STATUS_ID = C.STATUS_ID (+)
                    AND C.STATUS_ID = D.STATUS_ID (+)
                    AND E.USER_ID (+) = A.CREATED_BY
                    AND F.USER_ID (+) = A.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('CR','PR')
                    AND A.DOC_NO = ?
                    -- AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListFulfilmenOrderByPoCoFc($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,A.DOC_NO,
                     (SELECT DOC_NO FROM FL_FULFILMENT_REQUEST O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_NO,A.FULFILMENT_REQ_ID ,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('PO','CO','FC')   
                    AND A.DOC_NO  = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListDeliveryOrderByDoNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DELIVERY_ORDER_NO AS DOC_NO,A.SUPPLIER_DO_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_DELIVERY_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.DELIVERY_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?    
                    AND A.DELIVERY_ORDER_NO  = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY   B.CREATED_DATE DESC",
            array('en', 'DO', $docNo)
        );
        return $results;
    }

    public function getListFulfilmentNoteByFnNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.FULFILMENT_NOTE_NO AS DOC_NO, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_NOTE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.FULFILMENT_NOTE_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'FN', $docNo)
        );
        return $results;
    }

    public function getListQuotationTenderByQtNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.QT_ID,A.IS_PANEL,
                    A.QT_NO AS DOC_NO, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_QT A, SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.QT_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE not in 'SQ' 
                    AND A.QT_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListLetterAcceptanceByLoaNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                B.DOC_ID,B.DOC_TYPE,
                A.LOA_ID,
                A.LOA_NO AS DOC_NO, 
                D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                FROM SC_LOA A, SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                WHERE A.LOA_ID = B.DOC_ID
                AND B.STATUS_ID = C.STATUS_ID
                AND C.STATUS_ID = D.STATUS_ID
                AND E.USER_ID (+) = B.CREATED_BY
                AND F.USER_ID (+) = B.CHANGED_BY
                AND D.LANGUAGE_CODE = ?
                AND B.DOC_TYPE in 'LA' 
                AND A.LOA_NO = ? 
                ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListSMByApplNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.APPL_ID,A.SUPPLIER_TYPE,A.APPL_TYPE,
                    A.APPL_NO AS DOC_NO,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SM_APPL A, SM_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.APPL_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND A.APPL_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListStopInstructionBySdNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR(B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.STOP_INSTR_NO as doc_no,A.FULFILMENT_REQ_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_STOP_INSTR A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.STOP_INSTR_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ? 
                    AND A.STOP_INSTR_NO = ?  
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'SD', $docNo)
        );
        return $results;
    }

    public function getListApplicationNoByApNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT TO_CHAR(B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                B.DOC_ID,B.DOC_TYPE,
                A.APPL_NO as doc_no,A.ACCESS_APPL_ID,
                D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                FROM PM_ACCESS_APPL A, PM_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                WHERE A.ACCESS_APPL_ID = B.DOC_ID
                AND B.STATUS_ID = C.STATUS_ID
                AND C.STATUS_ID = D.STATUS_ID
                AND E.USER_ID (+) = B.CREATED_BY
                AND F.USER_ID (+) = B.CHANGED_BY
                AND D.LANGUAGE_CODE = ?
                AND B.DOC_TYPE IN ('MR','CP') 
                AND A.APPL_NO = ? 
                ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }


    public function getListInvoiceByInvNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.INVOICE_NO AS DOC_NO,A.SUPPLIER_INVOICE_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY  , A.*,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_INVOICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.INVOICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.INVOICE_NO = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'IN', $docNo)
        );
        return $results;
    }

    public function getListAdjustmentByDocNo($docNo)
    {
        // C0251062601070003 B0381201011100015
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,FULFILMENT_ORDER_ID, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_ADJUSTMENT A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.ADJUSTMENT_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE IN ('CN','DN')
                    AND A.DOC_NO  = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    public function getListPaymentAdviseByPaNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.PAYMENT_ADVICE_NO AS DOC_NO,
                    A.PAYMENT_REF_NO, A.FULFILMENT_REQ_ID,INVOICE_ID, A.FULFILMENT_ORDER_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_PAYMENT_ADVICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PAYMENT_ADVICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PAYMENT_ADVICE_NO = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PA', $docNo)
        );
        return $results;
    }

    public function getListWorkFlowStatusDOFN($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select(
            "
        SELECT
            (SELECT doc_no
             FROM fl_fulfilment_order
             WHERE fulfilment_req_id = fdo.FULFILMENT_REQ_ID)               poco_no,
            fdo.DELIVERY_ORDER_NO,
            (SELECT do1.status_id
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND
                   do1.status_id = do2.status_id AND do1.is_current = 1 AND do2.language_code = 'en' AND
                   do1.doc_type IN ('DO'))                                  do_status,
            (SELECT do2.status_name
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND do1.status_id = do2.status_id AND do1.is_current = 1
                   AND do2.language_code = 'en' AND do1.doc_type IN ('DO')) do_status_name,
            nvl(frn.FULFILMENT_NOTE_NO, 'Tiada')                              FRN_NO,
            (SELECT frn1.status_id
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status,
            (SELECT frn2.status_name
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status_name,
            fdo.RECORD_STATUS                                               do_record_status,
            frn.RECORD_STATUS                                               frn_record_status,
            fdo.DELIVERY_order_ID,
            frn.FULFILMENT_NOTE_ID
          FROM fl_fulfilment_note frn, fl_fulfilment_request pr, fl_fulfilment_order po, fl_delivery_order fdo
          WHERE
            pr.FULFILMENT_REQ_ID = po.FULFILMENT_REQ_ID
            AND fdo.FULFILMENT_ORDER_ID = po.FULFILMENT_ORDER_ID
            AND fdo.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID (+)
            AND po.DOC_NO = ? ",
            array($docNo)
        );

        return $query;
    }

    public function getListYepMenuTaskList($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        select * from fl_yep_tasklist
            WHERE doc_no = ? ", array($docNo));

        return $query;
    }

    public function getListQtPublished()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60007 
                           then qt.PUBLISH_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60007) and trunc(qt.publish_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtClosing()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60009
                           then qt.CLOSING_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60009) and trunc(qt.closing_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtPendingReschedulePublication()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60035) and trunc(qt.publish_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtPendingRescheduleProposalClosingDate()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60036) and trunc(qt.closing_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtPublishedByDay()
    {
        $query = "  SELECT
                    TO_CHAR(qt.publish_date,'YYYY-MM-DD') as publish_date,
                    (SELECT status_name
                     FROM pm_status_desc
                     WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                    wf.status_id                                              AS QT_STATUS_ID,
                    count (* ) as total
                  FROM sc_qt qt
                    INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                  WHERE wf.STATUS_ID IN (60007) AND trunc(qt.publish_date) > sysdate
                  GROUP BY TO_CHAR(qt.publish_date,'YYYY-MM-DD'),wf.status_id
                  ORDER BY 1 asc
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtPublishedByMinistry($date)
    {
        $query = "  SELECT
                TO_CHAR(qt.publish_date, 'YYYY-MM-DD') as publish_date,
                (SELECT status_name
                 FROM pm_status_desc
                 WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                wf.status_id                                              AS QT_STATUS_ID,
                d.org_code                                                AS MINISTRY_CODE,
                d.org_name                                                AS MINISTRY_NAME,
                count(*)                                                  AS total
              FROM sc_qt qt, sc_workflow_status wf,
                pm_org_validity d,
                pm_org_profile w,
                pm_org_profile m,
                pm_org_profile j,
                pm_org_profile p
              WHERE p.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FPJ')              -- Ptj
                    AND j.org_profile_id = p.parent_org_profile_id
                    AND j.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FJA')          -- Jabatan
                    AND m.org_profile_id = j.parent_org_profile_id
                    AND w.org_profile_id = m.parent_org_profile_id
                    AND w.org_type_id IN (
                              SELECT parameter_id
                              FROM pm_parameter
                              WHERE parameter_type = 'OT'
                                    AND parameter_code = 'FMI')      -- Kementerian
                    AND d.org_profile_id = w.org_profile_id
                    AND qt.org_profile_id = p.org_profile_id
                    AND d.record_status = 1
                    AND qt.qt_id = wf.doc_id
                    AND wf.doc_type = 'QT' AND wf.is_current = 1
                    AND wf.STATUS_ID IN (60007) AND trunc(qt.publish_date) = TO_DATE(?, 'YYYY-MM-DD')
              GROUP BY TO_CHAR(qt.publish_date, 'YYYY-MM-DD'), wf.status_id, d.org_code, d.org_name
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($date));
    }

    public function getListQtClosingByDay()
    {
        $query = "  SELECT
                    TO_CHAR(qt.closing_date,'YYYY-MM-DD')  as closing_date,
                    (SELECT status_name
                     FROM pm_status_desc
                     WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                    wf.status_id                                              AS QT_STATUS_ID,
                    count (* ) as total
                  FROM sc_qt qt
                    INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                  WHERE wf.STATUS_ID IN (60009) AND trunc(qt.closing_date) > sysdate
                  GROUP BY TO_CHAR(qt.closing_date,'YYYY-MM-DD'),wf.status_id
                  ORDER BY 1 asc
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    public function getListQtClosingByMinistry($date)
    {
        $query = "  SELECT
            TO_CHAR(qt.closing_date, 'YYYY-MM-DD') as closing_date,
            (SELECT status_name
             FROM pm_status_desc
             WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
            wf.status_id                                              AS QT_STATUS_ID,
            d.org_code                                                AS MINISTRY_CODE,
            d.org_name                                                AS MINISTRY_NAME,
            count(*)                                                  AS total
          FROM sc_qt qt, sc_workflow_status wf,
                pm_org_validity d,
                pm_org_profile w,
                pm_org_profile m,
                pm_org_profile j,
                pm_org_profile p
              WHERE p.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FPJ')              -- Ptj
                    AND j.org_profile_id = p.parent_org_profile_id
                    AND j.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FJA')          -- Jabatan
                    AND m.org_profile_id = j.parent_org_profile_id
                    AND w.org_profile_id = m.parent_org_profile_id
                    AND w.org_type_id IN (
                              SELECT parameter_id
                              FROM pm_parameter
                              WHERE parameter_type = 'OT'
                                    AND parameter_code = 'FMI')      -- Kementerian
                    AND d.org_profile_id = w.org_profile_id
                    AND qt.org_profile_id = p.org_profile_id
                    AND d.record_status = 1
                AND qt.qt_id = wf.doc_id
                AND wf.doc_type = 'QT' AND wf.is_current = 1
                AND wf.STATUS_ID IN (60009) AND trunc(qt.closing_date) = TO_DATE(?, 'YYYY-MM-DD')
          GROUP BY TO_CHAR(qt.closing_date, 'YYYY-MM-DD'), wf.status_id, d.org_code, d.org_name
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query, array($date));
    }
}