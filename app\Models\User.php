<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use LdapRecord\Laravel\Auth\AuthenticatesWithLdap;
use LdapRecord\Laravel\Auth\LdapAuthenticatable;

class User extends Authenticatable implements LdapAuthenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use SoftDeletes, HasApiTokens, HasFactory, Notifiable, AuthenticatesWithLdap;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'name',
        'user_name',
        'email',
        'password',
        'is_active',
        'is_ldap',
        'is_crm',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * The roles that belong to the user.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'users_roles', 'user_id', 'role_id')
            ->withTimestamps();
    }

    /**
     * Check if the user has the given role (directly or through groups).
     *
     * @param string $role
     * @return bool
     */
    public function hasRole($role)
    {
        // Ensure we're working with a string
        if (!is_string($role)) {
            throw new \InvalidArgumentException('Role must be a string');
        }

        // Create a cache key based on user ID and role
        $cacheKey = "user_{$this->id}_role_{$role}";

        // Check if result is in cache
        if (cache()->has($cacheKey)) {
            return cache()->get($cacheKey);
        }

        // Check direct role assignments
        $hasDirectRole = $this->roles()->where('role_code', $role)->exists();

        $hasRole = $hasDirectRole;

        // If not found via direct roles, check via groups
        if (!$hasRole) {
            $hasRole = $this->groups()
                ->whereHas('roles', function ($query) use ($role) {
                    $query->where('role_code', $role);
                })
                ->exists();
        }

        // Cache the result for 24 hours
        cache()->put($cacheKey, $hasRole, 60 * 60 * 24);

        return $hasRole;
    }

    /**
     * The groups that belong to the user.
     */
    public function groups()
    {
        return $this->belongsToMany(Group::class, 'users_groups', 'user_id', 'group_id')
            ->withTimestamps();
    }

    /**
     * Check if the user belongs to the given group.
     *
     * @param string $group
     * @return bool
     */
    public function hasGroup($group)
    {
        // Ensure we're working with a string
        if (!is_string($group)) {
            throw new \InvalidArgumentException('Group must be a string');
        }

        // Create a cache key based on user ID and group
        $cacheKey = "user_{$this->id}_group_{$group}";

        // Check if result is in cache
        if (cache()->has($cacheKey)) {
            return cache()->get($cacheKey);
        }

        // Check if user belongs to the group
        $hasGroup = $this->groups()->where('name', $group)->exists();

        // Cache the result for 24 hours
        cache()->put($cacheKey, $hasGroup, 60 * 60 * 24);

        return $hasGroup;
    }

    /**
     * Check if the user has access to the specified route with the specified action via their roles or groups.
     *
     * @param string $routeName
     * @param string $action The action to check: 'create', 'read', 'update', 'delete'
     * @return bool
     */
    public function canAccessRoute($routeName, $action = null)
    {
        // Create a cache key based on user ID, route name and action
        $cacheKey = "user_{$this->id}_route_{$routeName}_action_" . ($action ?? 'any');

        // Check if result is in cache
        if (cache()->has($cacheKey)) {
            return cache()->get($cacheKey);
        }

        // Not in cache, check via database
        // Build the query for permissions
        $permissionQuery = function ($query) use ($routeName, $action) {
            $query->where('route_name', $routeName);

            // If action is specified, check for it too
            if ($action) {
                $query->where('action', $action);
            }
        };

        // Check via direct roles
        $hasAccessViaDirectRoles = $this->roles()
            ->whereHas('permissions', $permissionQuery)
            ->exists();

        $hasAccess = $hasAccessViaDirectRoles;

        // If not found via direct roles, check via group roles
        if (!$hasAccess) {
            $hasAccess = $this->groups()
                ->whereHas('roles', function ($query) use ($permissionQuery) {
                    $query->whereHas('permissions', $permissionQuery);
                })
                ->exists();
        }

        // Cache the result for 24 hours (can adjust the time as needed)
        cache()->put($cacheKey, $hasAccess, 60 * 60 * 24);

        return $hasAccess;
    }
}
