<?php

namespace App\Livewire\Module\RevenueReport\Tabs;

use App\Livewire\Forms\RevenueReport\DailySummarySearchForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;

class SmTab extends Component {

    // Public properties to store data
    public $listdataSr = []; 
    public $listdataRegistration = []; 
    public $listdataTraining = []; 
    public $listdataSoftcert = []; 
    public $listdataMembership = []; 

    public function boot(RevenueService $RevenueService)
    {
        $this->revenueService = $RevenueService;
    }

    public function mount()
    { 
        try{
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $dateTemp = Carbon::parse($yesterday)->format('d-m-Y');
            $listSr = $this->revenueService->cmsListSummarySmSRDailyByDate($dateTemp);
            $listRegistration = $this->revenueService->cmsListSummarySmRegistrationDailyByDate($dateTemp);
            $listTraining = $this->revenueService->cmsListSummarySmTrainingDailyByDate($dateTemp);
            $listSoftcert = $this->revenueService->cmsListSummarySmSoftcertDailyByDate($dateTemp);
            $listMembership = $this->revenueService->cmsListSummarySmMembershipDailyByDate($dateTemp);
            
            $this->listdataSr = $listSr;
            $this->listdataRegistration = $listRegistration;
            $this->listdataTraining = $listTraining;
            $this->listdataSoftcert = $listSoftcert;
            $this->listdataMembership = $listMembership;
        } catch (\Exception $e) {
            $this->listdataSr = [];
            $this->listdataRegistration = [];
            $this->listdataTraining = [];
            $this->listdataSoftcert = [];
            $this->listdataMembership = [];
        } finally {
        }
        
    }

    public function render()
    {
        return view('livewire.revenue-report.tabs.sm-tab', [
            'listdataSr' => $this->listdataSr,
            'listdataRegistration' => $this->listdataRegistration,
            'listdataTraining' => $this->listdataTraining,
            'listdataSoftcert' => $this->listdataSoftcert,
            'listdataMembership' => $this->listdataMembership,
        ]);
    }
}