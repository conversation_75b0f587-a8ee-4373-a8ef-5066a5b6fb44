<?php

namespace App\Models\External;

use Illuminate\Database\Eloquent\Model;

class EpSupportMonitoringQtEp extends Model {
    protected $connection= 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_monitor_qt";
  
    public static function createMonitoringQtEp($obj,$type) {
        $check = EpSupportMonitoringQtEp::where('qt_id' ,$obj->qt_id)
                ->where('qt_monitor_type',$type)->count();
        if($check == 0){
            $qtObj = new EpSupportMonitoringQtEp;
            $qtObj->qt_monitor_type = $type;
            $qtObj->qt_id = $obj->qt_id;
            $qtObj->qt_no = $obj->qt_no;
            $qtObj->qt_status_id = $obj->qt_status_id;
            $qtObj->qt_status = $obj->qt_status;
            $qtObj->qt_date = $obj->qt_date;
            $qtObj->save();
        }
    }
    
}
