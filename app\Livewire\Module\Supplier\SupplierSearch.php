<?php

namespace App\Livewire\Module\Supplier;

use Livewire\Component;
use App\Services\SupplierService;
use Livewire\Attributes\Url;
use App\Livewire\Forms\Supplier\SupplierSearchForm;

abstract class SupplierSearch extends Component
{
    public SupplierSearchForm $supplierSearchForm;

    #[Url]
    public $searchValue = null;

    public $results = [];

    protected SupplierService $supplierService;

    public function boot(SupplierService $supplierService)
    {
        $this->supplierService = $supplierService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($this->searchValue) {
            $this->supplierSearchForm->search = $this->searchValue;
            $this->searchSuppliers();
        }
    }

    public function rendered()
    {
        $this->dispatch('suppliersUpdated');
    }

    /**
     * Determine search type based on input pattern
     * 
     * @param string $search The search string
     * @return string The determined search type
     */
    protected function getSearchType($search)
    {
        $search = trim($search);

        // MOF search pattern
        if (substr($search, 0, 4) === '357-' || substr($search, 0, 4) === '465-') {
            return "MOF";
        }

        // ePNo search pattern
        if (substr($search, 0, 3) === 'eP-') {
            return "ePNo";
        }

        // IcNo search pattern
        if (strlen($search) === 12 || (strlen(intval($search)) === 9)) {
            return "IcNo";
        }

        // Application number pattern
        $appPrefixes = ['JR', 'JN', 'KR', 'KN', 'KU', 'KA', 'KB', 'KC'];
        if (in_array(substr($search, 0, 2), $appPrefixes)) {
            return "ApplNo";
        }

        // Check if it might be a SAP vendor code (purely numeric)
        if (is_numeric($search)) {
            return "SAP";
        }

        // Default to supplier name
        return "SUPPLIER_NAME";
    }

    /**
     * Check if User can access this component
     * This method should be implemented by child classes
     */
    abstract public function userCan(string $action);

    /**
     * Search for suppliers based on the input
     * This method should be implemented by child classes
     */
    abstract public function searchSuppliers();

    /**
     * Render the component view
     * This method should be implemented by child classes
     */
    abstract public function render();
}