<?php

namespace App\Livewire\Module\BpmApi;

use Auth;
use Livewire\Component;
use App\Livewire\Forms\Bpm\FindTaskForm;
use App\Services\BpmApiService;
use Log;

class ServiceManager extends Component
{
    public FindTaskForm $form;
    public $results = [];
    public $errorMessage = null;
    public $module = null;
    public $doc_no = null;
    public $created_date = null;

    protected BpmApiService $bpmApiService;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('bpm.bpm-api.service-manager', $action);
    }

    public function boot(BpmApiService $bpmApiService)
    {
        $this->bpmApiService = $bpmApiService;
    }

    public function mount()
    {
        $this->search();
    }

    public function search()
    {

    }

    public function render()
    {
        return view('livewire.bpm-api.service-manager', [
            'tasks' => $this->results,
        ]);
    }
}
