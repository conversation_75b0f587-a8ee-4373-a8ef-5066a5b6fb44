<?php

namespace App\Livewire\Forms\Supplier;

use Livewire\Attributes\Validate;
use Livewire\Form;

class SupplierSearchForm extends Form
{
    #[Validate('required|min:2')]
    public $search = '';

    public function messages()
    {
        return [
            'search.required' => 'Please enter a search term',
            'search.min' => 'Search term must be at least 2 characters',
        ];
    }
}