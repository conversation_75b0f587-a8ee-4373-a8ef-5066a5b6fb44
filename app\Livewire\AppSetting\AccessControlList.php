<?php

namespace App\Livewire\AppSetting;

use App\Livewire\Forms\AppSetting\RoleForm;
use Auth;
use Livewire\Component;
use App\Models\User;
use App\Models\Role;
use App\Models\Group;
use App\Models\Permission;

class AccessControlList extends Component
{
    public RoleForm $roleForm;

    public $selectedRoleId;
    public $role;
    public $selectedPermissions = [];
    public $selectedGroupsToAssign = [];
    public $allRoutes = [];
    public $searchTerm = '';
    public $selectedUserIds = [];
    public $availableUsers = [];
    public $availableGroups = [];

    // Modal state properties
    public $isRoleModalOpen = false;
    public $isDeleteRoleModalOpen = false;
    public $isAssignGroupsModalOpen = false;
    public $isAddMemberModalOpen = false;
    public $roleIdToDelete = null;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    private function authorizeAction(string $action): void
    {
        if (!$this->userCan($action)) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $this->refreshRoutesList();
    }

    public function rendered()
    {
        $this->dispatch('refresh-role-members-table');
        $this->dispatch('refresh-role-groups-table');
    }

    // Role Modal Methods
    public function openAddRoleModal()
    {
        $this->authorizeAction('create');
        $this->roleForm->resetForm();
        $this->isRoleModalOpen = true;
        $this->dispatch('open-role-modal');
    }

    public function editRole($id)
    {
        $this->authorizeAction('update');
        $role = Role::find($id);
        if ($role) {
            $this->roleForm->edit($role);
            $this->isRoleModalOpen = true;
            $this->dispatch('open-role-modal');
        }
    }

    public function closeModal()
    {
        $this->isRoleModalOpen = false;
        $this->roleForm->resetForm();
    }

    public function saveRole()
    {
        $action = $this->roleForm->roleId ? 'update' : 'create';
        $this->authorizeAction($action);

        $this->roleForm->validate();

        $this->roleForm->save();
        $this->roleForm->resetForm();
        $this->isRoleModalOpen = false;
        session()->flash('message', 'Role saved successfully!');
        $this->dispatch('role-saved');
    }

    // Delete Role Modal Methods
    public function confirmDeleteRole($id)
    {
        $this->authorizeAction('delete');
        $this->roleIdToDelete = $id;
        $this->isDeleteRoleModalOpen = true;
        $this->dispatch('open-delete-role-modal');
    }

    public function closeDeleteModal()
    {
        $this->isDeleteRoleModalOpen = false;
        $this->roleIdToDelete = null;
    }

    public function executeDeleteRole()
    {
        $this->authorizeAction('delete');

        if ($this->roleIdToDelete) {
            // Check if role is being used
            $role = Role::with(['users', 'groups'])->find($this->roleIdToDelete);

            if ($role && ($role->users->count() > 0 || $role->groups->count() > 0)) {
                session()->flash('error', 'Cannot delete role that is assigned to users or groups.');
                $this->closeDeleteModal();
                return;
            }

            Role::destroy($this->roleIdToDelete);

            // Clear selectedRoleId if it was the deleted role
            if ($this->selectedRoleId == $this->roleIdToDelete) {
                $this->selectedRoleId = null;
                $this->role = null;
                $this->selectedPermissions = [];
            }

            $this->roleIdToDelete = null;
            $this->isDeleteRoleModalOpen = false;
            session()->flash('message', 'Role deleted successfully!');
            $this->dispatch('role-deleted');
        }
    }

    protected function refreshRoutesList()
    {
        // Get all unique route names from permissions
        $routeNames = Permission::select('route_name')->distinct()->get();

        $this->allRoutes = [];

        // For each route, determine which actions are available
        foreach ($routeNames as $routeItem) {
            $routeName = $routeItem->route_name;

            // Get all actions that exist for this route
            $availableActions = Permission::where('route_name', $routeName)
                ->pluck('action')
                ->toArray();

            $this->allRoutes[] = [
                'route_name' => $routeName,
                'available_actions' => $availableActions
            ];
        }
    }

    public function loadAccessControlListData()
    {
        $this->authorizeAction('read');

        if (!$this->selectedRoleId) {
            $this->role = null;
            $this->selectedPermissions = [];
            return;
        }

        $this->role = null;
        $this->role = Role::with(['permissions', 'users', 'groups'])->find($this->selectedRoleId);
        if (!$this->role) {
            return;
        }

        // Initialize permissions array
        $this->selectedPermissions = [];

        // Populate selected permissions based on role's current permissions
        foreach ($this->allRoutes as $route) {
            $routeName = $route['route_name'];
            $encodedRouteName = str_replace('.', '_dot_', $routeName);

            foreach (['create', 'read', 'update', 'delete'] as $action) {
                $hasPermission = $this->role->permissions
                    ->where('route_name', $routeName)
                    ->where('action', $action)
                    ->count() > 0;

                $this->selectedPermissions[$encodedRouteName][$action] = $hasPermission ? 1 : 0;
            }
        }

        // Reset the selected groups
        $this->selectedGroupsToAssign = [];

        // Load available users for adding to role
        $this->loadAvailableUsers();
    }

    public function loadAvailableGroups()
    {
        if (!$this->role)
            return;

        // Get groups not already in this role
        $roleGroupIds = $this->role->groups->pluck('id')->toArray();
        $this->availableGroups = Group::whereNotIn('id', $roleGroupIds)->get();
    }

    public function openAssignGroupsModal()
    {
        $this->authorizeAction('update');
        $this->loadAvailableGroups();
        $this->selectedGroupsToAssign = [];
        $this->isAssignGroupsModalOpen = true;
        $this->dispatch('open-assign-groups-modal');
    }

    public function closeAssignGroupsModal()
    {
        $this->isAssignGroupsModalOpen = false;
        $this->selectedGroupsToAssign = [];
    }

    public function assignGroupsToRole()
    {
        $this->authorizeAction('update');

        $this->validate([
            'selectedGroupsToAssign' => 'required|array',
            'selectedGroupsToAssign.*' => 'exists:groups,id',
        ]);

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        try {
            $addedCount = 0;
            $affectedUserIds = [];

            foreach ($this->selectedGroupsToAssign as $groupId) {
                if (!$this->role->groups->contains($groupId)) {
                    $this->role->groups()->attach($groupId);
                    $addedCount++;

                    // Get users in this group for cache clearing
                    $groupUserIds = \DB::table('users_groups')
                        ->where('group_id', $groupId)
                        ->pluck('user_id')
                        ->toArray();

                    $affectedUserIds = array_merge($affectedUserIds, $groupUserIds);
                }
            }

            // Clear role cache for all affected users
            foreach (array_unique($affectedUserIds) as $userId) {
                $cacheKey = "user_{$userId}_role_{$this->role->role_code}";
                cache()->forget($cacheKey);
            }

            if ($addedCount > 0) {
                session()->flash('message', $addedCount . ' group(s) added to role successfully!');
            } else {
                session()->flash('info', 'All selected groups are already in this role.');
            }

            // Reset and reload
            $this->selectedGroupsToAssign = [];
            $this->isAssignGroupsModalOpen = false;
            $this->loadAccessControlListData();
            $this->dispatch('groups-assigned');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add groups: ' . $e->getMessage());
        }
    }

    public function removeGroupFromRole($groupId)
    {
        $this->authorizeAction('update');

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        try {
            // Get users in this group for cache clearing before detaching
            $affectedUserIds = \DB::table('users_groups')
                ->where('group_id', $groupId)
                ->pluck('user_id')
                ->toArray();

            $this->role->groups()->detach($groupId);

            // Clear role cache for all affected users
            foreach ($affectedUserIds as $userId) {
                $cacheKey = "user_{$userId}_role_{$this->role->role_code}";
                cache()->forget($cacheKey);
            }

            session()->flash('message', 'Group removed from role successfully!');

            // Reload the role data to refresh the groups list
            $this->loadAccessControlListData();
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to remove group: ' . $e->getMessage());
        }
    }

    public function loadAvailableUsers()
    {
        if (!$this->role)
            return;

        // Get users not already in this role
        $roleUserIds = $this->role->users->pluck('id')->toArray();
        $this->availableUsers = User::whereNotIn('id', $roleUserIds)->get();
    }

    public function saveRolePermissions()
    {
        $this->authorizeAction('update');

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        // Start a transaction
        \DB::beginTransaction();

        try {
            // Get all users affected by this role change (direct members and via groups)
            $affectedUserIds = $this->role->users->pluck('id')->toArray();

            // Also include users who are in groups that have this role
            $groupUserIds = \DB::table('users_groups')
                ->whereIn('group_id', $this->role->groups->pluck('id'))
                ->pluck('user_id')
                ->toArray();

            $affectedUserIds = array_unique(array_merge($affectedUserIds, $groupUserIds));

            // Detach all existing permissions
            $this->role->permissions()->detach();

            // Attach the selected permissions
            foreach ($this->selectedPermissions as $encodedRouteName => $actions) {
                $routeName = str_replace('_dot_', '.', $encodedRouteName);

                foreach ($actions as $action => $value) {
                    if ($value) {
                        // Find or create the permission
                        $permission = Permission::firstOrCreate([
                            'route_name' => $routeName,
                            'action' => $action,
                        ], [
                            'name' => ucfirst($action) . ' ' . $routeName,
                            'description' => 'Permission to ' . $action . ' ' . $routeName,
                        ]);

                        // Attach permission to role
                        $this->role->permissions()->attach($permission->id);
                    }
                }
            }

            // Clear cache for all affected users
            foreach ($affectedUserIds as $userId) {
                // Clear route permissions cache
                $routeNames = Permission::pluck('route_name')->unique();
                $actions = ['create', 'read', 'update', 'delete', null];

                foreach ($routeNames as $routeName) {
                    foreach ($actions as $action) {
                        $cacheKey = "user_{$userId}_route_{$routeName}_action_" . ($action ?? 'any');
                        cache()->forget($cacheKey);
                    }
                }

                // Clear role cache
                $cacheKey = "user_{$userId}_role_{$this->role->role_code}";
                cache()->forget($cacheKey);
            }

            \DB::commit();
            session()->flash('message', 'Role permissions updated successfully!');

        } catch (\Exception $e) {
            \DB::rollBack();
            session()->flash('error', 'Failed to update permissions: ' . $e->getMessage());
        }

        // Reload role data to refresh permissions
        $this->loadAccessControlListData();
    }

    public function restoreDefaultPermissions()
    {
        $this->authorizeAction('read');

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        // Simply reload the role's current permissions from the database
        // This effectively discards any unsaved changes in the form
        $this->loadAccessControlListData();

        session()->flash('message', 'Form restored to current saved permissions.');
    }

    public function openAddMemberModal()
    {
        $this->authorizeAction('update');
        $this->loadAvailableUsers();
        $this->selectedUserIds = [];
        $this->isAddMemberModalOpen = true;
        $this->dispatch('open-add-member-modal');
    }

    public function closeAddMemberModal()
    {
        $this->isAddMemberModalOpen = false;
        $this->selectedUserIds = [];
    }

    public function addMemberToRole()
    {
        $this->authorizeAction('update');

        $this->validate([
            'selectedUserIds' => 'required|array',
            'selectedUserIds.*' => 'exists:users,id',
        ]);

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        try {
            $addedCount = 0;
            foreach ($this->selectedUserIds as $userId) {
                if (!$this->role->users->contains($userId)) {
                    $this->role->users()->attach($userId);
                    $addedCount++;

                    // Clear role cache for this user
                    $cacheKey = "user_{$userId}_role_{$this->role->role_code}";
                    cache()->forget($cacheKey);
                }
            }

            if ($addedCount > 0) {
                session()->flash('message', $addedCount . ' user(s) added to role successfully!');
            } else {
                session()->flash('info', 'All selected users are already in this role.');
            }

            // Reset and reload
            $this->selectedUserIds = [];
            $this->isAddMemberModalOpen = false;
            $this->loadAccessControlListData();
            $this->dispatch('member-added');

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to add users: ' . $e->getMessage());
        }
    }

    public function removeMember($userId)
    {
        $this->authorizeAction('update');

        if (!$this->role) {
            session()->flash('error', 'No role selected.');
            return;
        }

        try {
            $this->role->users()->detach($userId);

            // Clear role cache for this user
            $cacheKey = "user_{$userId}_role_{$this->role->role_code}";
            cache()->forget($cacheKey);

            session()->flash('message', 'User removed from role successfully!');

            // Reload role data
            $this->loadAccessControlListData();

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to remove user: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.app-setting.access-control-list', [
            'roles' => Role::all(),
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}