<?php

namespace App\Services;

use DB;
use Carbon\Carbon;
use Log;
use GuzzleHttp\Client;

class CRMService
{
    public function getDashboardTopEnqSubCategory()
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  clist.value_name AS subCategory ,clistt.value_name AS subCategory2,clist.value_code as subCatCode ,clistt.value_code as subCatCode2,COUNT(c.case_number) AS totalCases
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c = 'enquiry'
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                GROUP BY clistt.value_name,clist.value_name,clist.value_code,clistt.value_code
                ORDER BY COUNT(clistt.value_name) DESC , clist.value_name ASC
                LIMIT 10",
            array($processDate)
        );

        return $results;
    }

    public function getDashboardTopIncServSubCategory()
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  clist.value_name AS subCategory ,clistt.value_name AS subCategory2,clist.value_code as subCatCode ,clistt.value_code as subCatCode2 ,COUNT(c.case_number) AS totalCases
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c IN ('service', 'incident')
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                GROUP BY clistt.value_name,clist.value_name,clist.value_code,clistt.value_code
                ORDER BY COUNT(clistt.value_name) DESC , clist.value_name ASC
                LIMIT 10",
            array($processDate)
        );

        return $results;
    }

    public function getDashboardCS($contactMode = null, $caseInfo = null, $date = null)
    {

        $query = DB::connection('mysql_crm')->table('cases as c');
        $query->join('cases_cstm as cc', 'cc.id_c', '=', 'c.id');
        $query->leftJoin('users as u', 'c.pickupby_id', '=', 'u.id');
        $query->leftJoin('users as us', 'c.modified_user_id', '=', 'us.id');
        $query->where('c.deleted', 0);
        $query->whereIn('c.created_by', ['1be60b16-974a-11c0-cb30-591eb0147a82', '96fa954b-8288-4bef-ac39-5338a5b5c9c0']);
        if ($contactMode !== null) {
            $query->where('cc.contact_mode_c', $contactMode);
        } else {
            $query->whereIn('cc.contact_mode_c', ["Open Portal", "Email"]);
        }

        if ($caseInfo === 'info') {
            $query->where('cc.case_info_c', '=', 'info_notcompleted');
        }

        if ($date !== null) { //use in view dashboard_cs
            $query->where(DB::raw("STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d')"), $date);
        } else { // use in view crmdashboard/dashboard_cs -> show for all dates 
            $query->where('c.state', '=', 'Open');
            $query->where('c.status', '=', 'Open_Pending Input');
        }
        $query->select(
            'c.case_number AS case_number',
            'c.name AS case_name',
            'u.first_name AS csName',
            'us.first_name AS csNama',
            'c.pickup_datetime AS case_pickupdate',
            'c.date_entered AS created_date',
            'c.date_modified AS modified_date',
            'cc.request_type_c AS request_type',
            'cc.incident_service_type_c AS type_of_incident',
            'c.state AS case_state',
            'c.status AS case_status',
            'c.sla_flag AS cs_sla_flag',
            'cc.contact_mode_c AS contact_mode',
            'cc.case_info_c AS case_info',
            'c.id AS case_id'
        );
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') as cs_start_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00') as cs_due_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') as cs_actual_start_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00') as cs_completed_datetime"));
        $query->addSelect(DB::raw("CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')) END AS cs_available_duration"));
        $query->addSelect(DB::raw("CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00')) END AS cs_actual_duration"));

        $data = $query->get();
        return $data;
    }

    public function getDashboardPendingResponse()
    {

        $processDate = Carbon::yesterday()->toDateString();
        // Log::info(self::class . ' Hari ini : ' . $processDate);

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.id     AS case_id,
                 c.case_number     AS case_number,
                 c.name            AS case_name,
                 u.first_name     AS csName,
                 us.first_name AS csNama,
                 c.pickup_datetime AS case_pickupdate,
                 CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS created_date,
                 cc.request_type_c                            AS request_type,
                 cc.incident_service_type_c                   AS type_of_incident,
                 c.state                                     AS case_state,
                 c.status                                    AS case_status,
                 c.sla_flag                                  AS cs_sla_flag,
                 cmode.value_name                             AS contact_mode,
                 cc.case_info_c                              AS case_info,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_start_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')   AS cs_due_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_actual_start_datetime,
                 CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00') AS cs_completed_datetime,
                 NOW(),
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')) END)   AS cs_available_duration,
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00')) END) AS cs_actual_duration 
                FROM (cases c
                JOIN cases_cstm cc ON (c.id = cc.id_c)
                LEFT JOIN cstm_list_app cmode ON (cc.contact_mode_c = cmode.value_code)
                LEFT JOIN users u ON (u.`id`= c.`pickupby_id`)
                LEFT JOIN users us ON (us.`id`= c.`modified_user_id`))
                WHERE( (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                AND ( cmode.`value_name` IN ('Open Portal','Email'))
                AND (c.`created_by` IN ('1be60b16-974a-11c0-cb30-591eb0147a82','96fa954b-8288-4bef-ac39-5338a5b5c9c0')) 
                AND (c.cntc_mode_executiondate IS NOT NULL)    
                AND (c.`status` = 'Open_Pending Input') 
                AND (cc.`request_type_c` = '')
                AND (cc.`case_info_c` = '')                  
                AND (`c`.`deleted` = 0))
                ORDER BY cs_completed_datetime ASC",
            array($processDate)
        );

        return $results;
    }

    public function getLatestJobQue($name)
    {
        $query = DB::connection('mysql_crm')->table('job_queue');
        $query->where('name', $name);
        $query->orderBy('execute_time', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailsTopEnqSubCategory($subCat, $subCat2)
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.`case_number` as caseNum,c.`date_entered` AS caseCreated,cc.`contact_mode_c` as contactMode,cc.`request_type_c` as requestType,clist.value_name AS subCategory ,clistt.value_name AS subCategory2
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c = 'enquiry'
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                AND clist.`value_code` = ?
                AND clistt.`value_code` =?
                ORDER BY c.`date_entered` DESC",
            array($processDate, $subCat, $subCat2)
        );

        return $results;
    }

    public function getDetailsTopIncServSubCategory($subCat, $subCat2)
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.`case_number` as caseNum,c.`date_entered` AS caseCreated,cc.`contact_mode_c` as contactMode,cc.`request_type_c` as requestType,clist.value_name AS subCategory ,clistt.value_name AS subCategory2
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c IN ('service', 'incident')
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                AND clist.`value_code` = ?
                AND clistt.`value_code` =?
                ORDER BY c.`date_entered` DESC",
            array($processDate, $subCat, $subCat2)
        );

        return $results;
    }
}