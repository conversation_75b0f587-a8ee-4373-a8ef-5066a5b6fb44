<?php

namespace App\Livewire\Module\Item;

use App\Livewire\Forms\Item\ItemHistorySearchForm;
use App\Services\ItemService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;
use Excel;
use App\Exports\ItemHistoryExport;
use PDF;

class ItemHistory extends Component
{
    protected ItemService $ItemService;

    public ItemHistorySearchForm $itemHistorySearchForm;

    // Public properties to store data
    public $listdata = []; 
    #[Url]
    public $carian = null;
    public $date = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public function boot(ItemService $ItemService)
    {
        $this->itemService = $ItemService;
    }

    public function mount()
    {
        $this->date = Carbon::now()->format('Y-m-d');
        $this->itemHistorySearchForm->date = $this->date;
        if($this->carian) {
            $this->itemHistorySearchForm->carian = $this->carian;
        }
        $this->search(); 
    }

    public function rendered()
    {
        $this->dispatch('refresh-item-history-table');
    } 

    public function search()
    {
        $this->itemHistorySearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->itemHistorySearchForm->carian;
        $this->date = $this->itemHistorySearchForm->date;
        $this->errorMessage = null;

        try {
            $list = null;
            $carianTemp = trim(strtoupper($this->carian));
            $dateTemp = Carbon::parse($this->date)->format('Y-m-d');
            $subStrCheck = substr($this->carian, 0, 3);
            $carianLen = strlen($carianTemp); 
            if($subStrCheck == 'RNC' || $subStrCheck == 'RNE'){
                $list = $this->itemService->getListProductSupplierCodificationTask('DOC_NO', $carianTemp,$this->date);
            }
            else {
                $list = $this->itemService->getListProductSupplierCodificationTask('LIKE_SEARCH', '%'.$carianTemp.'%',$this->date);
            }
            $this->listdata = $list;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
        }  
    } 

    public function render()
    {
        return view('livewire.item.item-history', [
            'listdata' => $this->listdata
        ]);
    }

    public function exportToExcel()
    {  
        return Excel::download(new ItemHistoryExport($this->listdata), 'item_history.xlsx'); 
    }

    public function exportToPdf()
    {  
        $data = [
            'listdata' => $this->listdata
        ]; 
        $pdf = PDF::loadView('partials.item.item-history.itempdf', $data);
        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
            }, 'item_task_history.pdf');
    }
}