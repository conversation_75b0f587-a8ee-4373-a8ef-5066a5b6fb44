<?php

namespace App\Livewire;

use Auth;
use Livewire\Component;
use Livewire\Attributes\Url;

class AppSetting extends Component
{
    #[Url]
    public $activeTab = 'users';

    public function mount()
    {
        // Get tab from URL
        $tab = request()->query('tab');
        $validTabs = ['users', 'groups', 'roles', 'permissions', 'access-control-list'];

        // Check if tab exists and is valid
        if (!$tab || !in_array($tab, $validTabs)) {
            // Set default tab to 'users'
            $this->activeTab = 'users';

            // Redirect to the same route with the 'users' tab in the URL
            return redirect()->to(route(request()->route()->getName(), ['tab' => 'users']));
        }

        // If we reach here, tab is valid
        $this->activeTab = $tab;
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        return redirect()->route('settings', ['tab' => $tab]);
    }

    /**
     * Check if current user has permission for a specific action on settings
     */
    public function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    public function render()
    {
        return view('livewire.app-setting', [
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}