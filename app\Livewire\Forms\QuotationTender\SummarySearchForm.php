<?php

namespace App\Livewire\Forms\QuotationTender;

use Livewire\Attributes\Validate;
use Livewire\Form;

class SummarySearchForm extends Form
{
    #[Validate('required|min:2')]
    public $search = '';

    public function messages()
    {
        return [
            'search.required' => 'Please enter a search term',
            'search.min' => 'Search term must be at least 2 characters',
        ];
    }
}