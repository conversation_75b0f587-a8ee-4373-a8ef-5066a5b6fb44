<?php

namespace App\Livewire\Module\Crm;

use App\Services\CRMService;
use Auth;
use DateTime;
use Livewire\Component;
use Carbon\Carbon;


class CrmCsPendingInput extends Component
{
    protected CRMService $crmService;

    public $dashboardData = [];

    // For Modal
    public $selectedContactMode;
    public $selectedInfo;
    public $modalData;
    public $modalTitle;
    public $isModalOpen = false;
    public $isLoading = false;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('crm.cs-pending-input', $action);
    }

    public function boot(CRMService $crmService)
    {
        $this->crmService = $crmService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        $this->dashboardData = $this->getDashboardCSPendInput();
    }

    private function getDashboardCSPendInput()
    {
        $totalAllEmail = 0;
        $totalPendActionEmail = 0;
        $totalPendMoreInfoEmail = 0;
        $totalAllOP = 0;
        $totalPendActionOP = 0;
        $totalPendMoreInfoOP = 0;

        $listCase = $this->crmService->getDashboardCS();

        if (count($listCase) > 0) {
            foreach ($listCase as $data) {
                if ($data->contact_mode === 'Email') {
                    if ($data->case_info === 'info_notcompleted') {
                        $totalPendMoreInfoEmail++;
                    } else {
                        $totalPendActionEmail++;
                    }
                    $totalAllEmail = $totalPendMoreInfoEmail + $totalPendActionEmail;
                } else {
                    if ($data->case_info === 'info_notcompleted') {
                        $totalPendMoreInfoOP++;
                    } else {
                        $totalPendActionOP++;
                    }
                    $totalAllOP = $totalPendMoreInfoOP + $totalPendActionOP;
                }
            }
        }

        return [
            'email' => [
                'total' => $totalAllEmail,
                'pending_action' => $totalPendActionEmail,
                'pending_more_info' => $totalPendMoreInfoEmail
            ],
            'open_portal' => [
                'total' => $totalAllOP,
                'pending_action' => $totalPendActionOP,
                'pending_more_info' => $totalPendMoreInfoOP
            ]
        ];
    }

    public function loadPendingInputData($contactMode, $info)
    {
        $this->selectedContactMode = $contactMode;
        $this->selectedInfo = $info;
        $this->isLoading = true;
        $this->isModalOpen = true;

        // Set modal title
        $contactModeLabel = ($contactMode == 'Email') ? 'Email' : 'Open Portal';
        $infoLabel = '';

        switch ($info) {
            case 'all':
                $infoLabel = 'Total Cases';
                break;
            case 'action':
                $infoLabel = 'Total Cases With Pending Action';
                break;
            case 'info':
                $infoLabel = 'Total Cases With Pending More Info';
                break;
        }

        $this->modalTitle = $infoLabel . ' (' . $contactModeLabel . ')';

        try {
            $this->modalData = $this->getDashboardCSPendInputDetail($contactMode, $info);
        } catch (\Exception $e) {
            $this->modalData = [];
        } finally {
            $this->isLoading = false;
        }

        $this->dispatch('open-cs-pending-input-modal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->modalData = [];
        $this->selectedContactMode = null;
        $this->selectedInfo = null;
    }

    private function getDashboardCSPendInputDetail($contactMode, $info)
    {
        $listCase = $this->crmService->getDashboardCS($contactMode, $info);
        $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');
        $listA = array(NULL, '');

        $formattedData = [];
        $counter = 0;

        foreach ($listCase as $value) {
            $createdDate = Carbon::parse($value->created_date)->addHours(8)->format("Y-m-d H:i:s");
            $modifiedDate = Carbon::parse($value->modified_date)->addHours(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if (in_array($value->case_status, $open)) {
                $ageing = $current->diff(new DateTime($createdDate));
            } else {
                $ageing = Carbon::parse($modifiedDate)->diff(new DateTime($createdDate));
            }

            $shouldInclude = false;
            if ($info === 'action') {
                $shouldInclude = in_array($value->case_info, $listA);
            } else {
                $shouldInclude = true;
            }

            if ($shouldInclude) {
                $formattedData[] = [
                    'counter' => ++$counter,
                    'case_id' => $value->case_id ?? '',
                    'case_number' => $value->case_number,
                    'aging_days' => $ageing->days,
                    'created_date' => $createdDate,
                    'modified_date' => $modifiedDate,
                    'case_state' => $value->case_state,
                    'case_status' => $value->case_status,
                    'contact_mode' => $value->contact_mode,
                    'case_info' => $value->case_info
                ];
            }
        }

        return $formattedData;
    }

    public function render()
    {
        return view('livewire.crm.cs-pending-input', [
            'dashboardData' => $this->dashboardData
        ]);
    }
}