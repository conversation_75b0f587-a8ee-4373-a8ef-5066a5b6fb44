<?php

namespace App\Livewire\Module\BpmApi;

use Livewire\Component;
use App\Livewire\Forms\Bpm\InstanceQuerySearchForm;
use App\Services\BpmApiService;
use App\Traits\BpmApiTrait;
use Carbon\Carbon;
use Log;

class InstanceQuery extends Component
{
    use BpmApiTrait;

    public InstanceQuerySearchForm $instanceQueryForm;
    public $results = [];
    public $errorMessage = null;
    
    public $newComposite = [];
    public $stateInstanceList = [];
    
    public $composite = null;
    public $from = null;
    public $to = null;
    public $selectedState = [];

    protected BpmApiService $bpmApiService;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('bpm.bpm-api.find-task', $action);
    }

    public function boot(BpmApiService $bpmApiService)
    {
        $this->bpmApiService = $bpmApiService;
    }

    public function mount()
    {

        $listComposite = $this->bpmApiService->apiServices('listComposites',null);
        $this->stateInstanceList = BpmApiTrait::$STATE_INSTANCE_LIST;
        if ($listComposite["status"] != null && $listComposite["status"] === 'Success') {
            $listdataComposite = $listComposite["result"];
        } 
        if (isset($listdataComposite["data"])) {
            $sortComposite = collect($listdataComposite["data"])->sortBy('composite')->toArray();
            foreach ($sortComposite as $data) { 
                $newComposite[$data['composite']] = $data['composite'];
            }
        }

        $this->newComposite = $newComposite;
        
        if($this->composite) {
            $this->instanceQueryForm->composite = $this->composite;
        }
        if($this->from) {
            $this->instanceQueryForm->from = $this->from;
        }
        if($this->to) {
            $this->instanceQueryForm->to = $this->to;
        }
        
        $this->instanceQueryForm->selectedState = $this->selectedState;
        $this->search();
    }

    public function search()
    {
        $this->instanceQueryForm->validate();

        $results = collect();
        try {
            $composite = $this->instanceQueryForm->composite;
            $dateFrom = Carbon::parse($this->instanceQueryForm->from)->format("d-m-Y");
            $dateTo = Carbon::parse($this->instanceQueryForm->to)->format("d-m-Y");
            $timeFrom = Carbon::parse($this->instanceQueryForm->from)->format("H:i");
            $timeTo = Carbon::parse($this->instanceQueryForm->to)->format("H:i");
            $selectedState = implode(",",$this->instanceQueryForm->selectedState);
             
            $data = collect([
                "composite" => $composite,
                "date_from" => $dateFrom,
                "date_to" => $dateTo,
                "time_from" => $timeFrom,
                "time_to" => $timeTo,
                "state" => $selectedState,
            ]);
            $results = $this->bpmApiService->apiServices('instance_query', $data);
            $this->results = $results;
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
        }

    }

    public function render()
    {
        return view('livewire.bpm-api.instance-query', [
            'result' => $this->results
        ]);
    }
}
