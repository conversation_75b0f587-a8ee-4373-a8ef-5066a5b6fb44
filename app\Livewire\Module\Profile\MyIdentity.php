<?php

namespace App\Livewire\Module\Profile;

use App\Livewire\Forms\Profile\MyIdentitySearchForm;
use Auth;
use Livewire\Attributes\Url;
use Livewire\Component;
use App\Services\SSHService;

class MyIdentity extends Component
{
    public MyIdentitySearchForm $myIdentitySearchForm;
    #[Url]
    public $searchValue = null;

    // Add these properties to store search results
    public $carian = '';
    public $result = [];
    public $status = '';
    public $statusDesc = '';

    protected SSHService $sshService;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('ep.profile.my-identity', $action);
    }

    public function boot(SSHService $sshService)
    {
        $this->sshService = $sshService;
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }

        if ($this->searchValue) {
            $this->myIdentitySearchForm->search = $this->searchValue;
            $this->searchMyIdentity();
        }
    }

    public function searchMyIdentity()
    {
        $this->myIdentitySearchForm->validate();
        $this->searchValue = $this->myIdentitySearchForm->search;

        $icNo = $this->myIdentitySearchForm->search;
        $this->carian = $icNo;

        if (strlen($icNo) == 0) {
            $this->result = [];
            $this->status = 'success';
            $this->statusDesc = '';
            return;
        }

        $data = $this->sshService->getMyIdentityInfo($icNo);

        $this->result = $data['result'];
        $this->status = $data['status'];
        $this->statusDesc = $data['statusDesc'];
    }

    public function render()
    {
        return view('livewire.profile.my-identity', [
            'carian' => $this->carian,
            'result' => $this->result,
            'status' => $this->status,
            'statusDesc' => $this->statusDesc
        ]);
    }
}