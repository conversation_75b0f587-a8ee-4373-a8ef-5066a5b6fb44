<?php

namespace App\Livewire\AppSetting;

use Auth;
use Livewire\Component;
use App\Models\Group;
use App\Models\Role;
use App\Livewire\Forms\AppSetting\GroupForm;

class Groups extends Component
{
    public GroupForm $groupForm;

    private function userCan(string $action): bool
    {
        $user = Auth::user();
        return $user && $user->canAccessRoute('settings', $action);
    }

    private function authorizeAction(string $action): void
    {
        if (!$this->userCan($action)) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function mount()
    {
        if (!$this->userCan('read')) {
            abort(403, 'You do not have permission to perform this action.');
        }
    }

    public function saveGroup()
    {
        $action = $this->groupForm->groupId ? 'update' : 'create';
        $this->authorizeAction($action);

        $this->groupForm->validate();

        $group = $this->groupForm->save();

        // If updating an existing group, clear group cache for all members
        if ($action === 'update') {
            $userIds = \DB::table('users_groups')
                ->where('group_id', $group->id)
                ->pluck('user_id')
                ->toArray();

            foreach ($userIds as $userId) {
                $cacheKey = "user_{$userId}_group_{$group->name}";
                cache()->forget($cacheKey);

                // Also clear role caches for roles this group has
                $roleIds = \DB::table('groups_roles')
                    ->where('group_id', $group->id)
                    ->pluck('role_id')
                    ->toArray();

                $roleCodes = Role::whereIn('id', $roleIds)->pluck('role_code')->toArray();

                foreach ($roleCodes as $roleCode) {
                    $roleCacheKey = "user_{$userId}_role_{$roleCode}";
                    cache()->forget($roleCacheKey);
                }
            }
        }

        $this->groupForm->resetForm();
        session()->flash('message', 'Group saved successfully!');
        $this->dispatch('group-saved');
    }

    public function editGroup($id)
    {
        $this->authorizeAction('update');
        $group = Group::find($id);
        $this->groupForm->edit($group);
    }

    public function deleteGroup($id)
    {
        $this->authorizeAction('delete');

        $group = Group::with('roles')->find($id);

        if ($group) {
            // Get users in this group
            $userIds = \DB::table('users_groups')
                ->where('group_id', $id)
                ->pluck('user_id')
                ->toArray();

            // Get roles this group had
            $roleCodes = $group->roles->pluck('role_code')->toArray();

            // Clear caches for all affected users
            foreach ($userIds as $userId) {
                // Clear group cache
                $cacheKey = "user_{$userId}_group_{$group->name}";
                cache()->forget($cacheKey);

                // Clear role caches for all roles this group had
                foreach ($roleCodes as $roleCode) {
                    $roleCacheKey = "user_{$userId}_role_{$roleCode}";
                    cache()->forget($roleCacheKey);
                }
            }

            Group::destroy($id);
        }

        session()->flash('message', 'Group deleted successfully!');
    }

    public function render()
    {
        return view('livewire.app-setting.groups', [
            'groups' => Group::all(),
            'canCreate' => $this->userCan('create'),
            'canUpdate' => $this->userCan('update'),
            'canDelete' => $this->userCan('delete'),
        ]);
    }
}