<?php

namespace App\Livewire\Forms\AppSetting;

use Livewire\Form;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserForm extends Form
{
    public $userId;
    public $firstName = '';
    public $lastName = '';
    public $userName = '';
    public $email = '';
    public $password = '';
    public $isLdap = false;
    public $isCrm = false;

    public function rules()
    {
        $rules = [
            'firstName' => 'required',
            'lastName' => 'required',
            'userName' => 'required|unique:users,user_name,' . $this->userId,
            'email' => 'required|email|unique:users,email,' . $this->userId,
        ];

        // Password is required only for new users when both LDAP and CRM are false
        if (!$this->userId && !$this->isLdap && !$this->isCrm) {
            $rules['password'] = 'required';
        }

        return $rules;
    }

    public function save()
    {
        $userData = [
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'name' => $this->firstName . ' ' . $this->lastName,
            'user_name' => $this->userName,
            'email' => $this->email,
            'is_ldap' => $this->isLdap,
            'is_crm' => $this->isCrm,
        ];

        if ($this->password) {
            $userData['password'] = Hash::make($this->password);
        } elseif (!$this->userId) {
            $userData['password'] = Hash::make(Str::random(16));
        }

        if ($this->userId) {
            $user = User::find($this->userId);
            $user->update($userData);
        } else {
            $user = User::create($userData);
        }

        return $user;
    }

    public function edit(User $user)
    {
        $this->userId = $user->id;
        $this->firstName = $user->first_name;
        $this->lastName = $user->last_name;
        $this->userName = $user->user_name;
        $this->email = $user->email;
        $this->isLdap = $user->is_ldap;
        $this->isCrm = $user->is_crm;
    }

    public function resetForm()
    {
        $this->userId = null;
        $this->firstName = '';
        $this->lastName = '';
        $this->userName = '';
        $this->email = '';
        $this->password = '';
        $this->isLdap = false;
        $this->isCrm = false;
    }
}