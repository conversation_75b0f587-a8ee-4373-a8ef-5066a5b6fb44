<?php

namespace App\Livewire\Module\RevenueReport;

use App\Livewire\Forms\RevenueReport\AccumulativeSearchForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\Url;
use Livewire\Component;
use Carbon\Carbon;
use App\Services\RevenueService;

class Accumulative extends Component
{
    protected RevenueService $RevenueService;
    public AccumulativeSearchForm $accumulativeSearchForm;

    // Public properties to store data
    public $listdata = [];
    public $dataStats = [];
    public $dataList = [];
    #[Url]
    public $carian = null;
    public $errorMessage = null;
    public $isSearching = false; 

    public function boot(RevenueService $RevenueService)
    {
        $this->revenueService = $RevenueService;
    }

    public function mount()
    {
        $year = Carbon::yesterday()->format('Y');
        if($this->carian) {
            $year = $this->carian;
        }
        $this->accumulativeSearchForm->carian = $year;
        $this->search();
    }

    public function rendered()
    {
        $this->dispatch('refresh-sr-summary-table');
        $this->dispatch('refresh-tr-summary-table');
    } 

    public function search()
    {
        $this->accumulativeSearchForm->validate();

        $this->isSearching = true;
        $this->carian = $this->accumulativeSearchForm->carian;
        $this->errorMessage = null;
        $dataList = collect([]);
        $summaryPvTr = collect([]);
        try {
            $list = $this->revenueService->getPaymentStatPercentage($this->carian);
            
            $dataSumAmount = array();
            $count = 1;
            $sumAll = 0;
            $currentEarn = 0;
            $highestEarnMth = '';
            $highestEarnAmount = '';
            if($list) {
                foreach ($list as $data) {
                    $dataList->push($data) ;
                    array_push($dataSumAmount, [$count, $data->sum_amount]);
        
                    $sumAll += $data->sum_amount;
                    $currentEarn = $data->sum_amount;
                    
                    if($currentEarn > $highestEarnAmount){
                        $highestEarnAmount = $currentEarn;
                        $highestEarnMth = $data->month;
                    }
                    
                    $count++;
                }
                $highestEarnAmt = number_format($highestEarnAmount,2);
                $summaryPvTr->put('summ_all',number_format($sumAll,2) );
                $summaryPvTr->put('current_earn',number_format($currentEarn,2) );
                $summaryPvTr->put('highest_earn_amount',$highestEarnAmt );
                $summaryPvTr->put('highest_earn_month',$highestEarnMth );
                $this->listdata = $summaryPvTr;
            }

            $countInfo = 0;
            $statProcessFeeAmt = array();
            $statRegisterFeeAmt = array();
            $statSoftcertFeeAmt = array();
            $currentMonth = date('m');
            $currentMonthAmt = 0;
            $statSumAll = 0;
            $statProcessTotal = 0;
            $statRegisterTotal = 0;
            $statSoftcertTotal = 0;
            $stathighestMth = '';
            $stathighestAmount = '';
            $paymentStatInfo = $this->revenueService->getPaymentStatInfo($this->carian);
            if($paymentStatInfo) {
                foreach ($paymentStatInfo as $pInfo) {
                    if($pInfo->bill_type === 'Processing Fee') {
                        $statProcessTotal += $pInfo->total_amt;
                        array_push($statProcessFeeAmt, [$pInfo->month, $pInfo->total_amt]);
                    } elseif($pInfo->bill_type === 'Registration Fee') {
                        $statRegisterTotal += $pInfo->total_amt;
                        array_push($statRegisterFeeAmt, [$pInfo->month, $pInfo->total_amt]);
                    } elseif($pInfo->bill_type === 'Softcert Fee') {
                        $statSoftcertTotal += $pInfo->total_amt;
                        array_push($statSoftcertFeeAmt, [$pInfo->month, $pInfo->total_amt]);
                    }
        
                    $statSumAll += $pInfo->total_amt;
        
                    if($pInfo->month == $currentMonth){
                        $currentMonthAmt += $pInfo->total_amt;
                    }
        
                    $countInfo++;
                }
                $paymentStatInfo[0]->current_month_total = $currentMonthAmt;
                $paymentStatInfo[0]->sum_process = $statProcessTotal;
                $paymentStatInfo[0]->sum_register = $statRegisterTotal;
                $paymentStatInfo[0]->sum_softcert = $statSoftcertTotal;
                $paymentStatInfo[0]->sum_all = $statSumAll;

                $this->dataStats = $paymentStatInfo;
            }
            
            $this->dataList = $dataList;
        } catch (\Exception $e) {
            $this->errorMessage = 'An error occurred while searching: ' . $e->getMessage();
            $this->listdata = [];
            $this->dataStats = [];
        }  
    } 

    public function render()
    {
        return view('livewire.revenue-report.accumulative', [
            'listdata' => $this->listdata,
            'dataStats' => $this->dataStats
        ]);
    }
}