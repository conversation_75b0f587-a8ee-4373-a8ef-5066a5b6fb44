<?php

namespace App\Livewire\Project;

use Livewire\Component;

class Bpm extends Component
{
    public $activeTab;

    public function mount()
    {
        $this->activeTab = request()->query('tab');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        return redirect()->route('bpm', ['tab' => $tab]);
    }

    public function render()
    {
        return view('livewire.project.bpm.index');
    }
}